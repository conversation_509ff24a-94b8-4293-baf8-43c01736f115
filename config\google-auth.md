# Google Authentication Setup for FeeFence

## 🔧 Setup Instructions

### 1. Google Cloud Console Setup

1. **Go to Google Cloud Console**: https://console.cloud.google.com/
2. **Create a new project** or select existing project
3. **Enable Google Sign-In API**:
   - Go to "APIs & Services" > "Library"
   - Search for "Google Sign-In API"
   - Click "Enable"

### 2. Create OAuth 2.0 Credentials

1. **Go to "APIs & Services" > "Credentials"**
2. **Click "Create Credentials" > "OAuth 2.0 Client IDs"**
3. **Configure OAuth consent screen** (if not done):
   - Choose "External" for testing
   - Fill in app name: "FeeFence"
   - Add your email as developer contact
4. **Create credentials for each platform**:

#### For Android:
- Application type: "Android"
- Package name: `com.feefence.app` (or your package name)
- SHA-1 certificate fingerprint: Get from `keytool` or Expo

#### For iOS:
- Application type: "iOS"
- Bundle ID: `com.feefence.app` (or your bundle ID)

#### For Web (Expo Web):
- Application type: "Web application"
- Authorized origins: `http://localhost:8081`

### 3. Update Configuration

Replace `YOUR_WEB_CLIENT_ID` in `services/GoogleAuthService.ts` with your actual Web Client ID:

```typescript
GoogleSignin.configure({
  webClientId: 'YOUR_ACTUAL_WEB_CLIENT_ID.apps.googleusercontent.com',
  // ... other config
});
```

### 4. For Expo Development

Add to your `app.json` or `app.config.js`:

```json
{
  "expo": {
    "plugins": [
      [
        "@react-native-google-signin/google-signin",
        {
          "iosUrlScheme": "com.feefence.app"
        }
      ]
    ]
  }
}
```

### 5. For Production Build

#### Android:
- Add `google-services.json` to `android/app/`
- Update `android/app/build.gradle`

#### iOS:
- Add `GoogleService-Info.plist` to iOS project
- Update iOS configuration

## 🔒 Security Notes

- **Never commit** actual client IDs to version control
- Use **environment variables** for production
- Keep **client secrets** secure
- Regularly **rotate credentials**

## 🧪 Testing

1. **Development**: Use localhost URLs
2. **Staging**: Add staging domain to authorized origins
3. **Production**: Add production domain to authorized origins

## 📱 Platform Support

- ✅ **Android**: Full support with Google Play Services
- ✅ **iOS**: Full support with native Google SDK
- ✅ **Web**: Full support with Google OAuth
- ⚠️ **Expo Go**: Limited support (use development build)

## 🚀 Current Implementation

The current implementation:
1. **Initializes** Google Sign-In with configuration
2. **Handles sign-in flow** with proper error handling
3. **Stores user data** locally for persistence
4. **Integrates** with existing mock auth system
5. **Supports sign-out** and access revocation

## 🔄 Next Steps

1. Get actual Google OAuth credentials
2. Update `GoogleAuthService.ts` with real client ID
3. Test on actual devices (not Expo Go)
4. Add proper error handling for production
5. Implement server-side token verification
