import { where, orderBy, limit } from 'firebase/firestore';
import FirestoreService from './firestore';
import { 
  Penalty, 
  PenaltyStatus, 
  PaymentMethod, 
  PaymentDetails, 
  COLLECTIONS, 
  FeeFenceError 
} from '@/types';
import UserService from './userService';

export class PenaltyService {
  // Create a new penalty
  static async createPenalty(
    userId: string,
    blockedAppId: string,
    appName: string,
    amount: number,
    paymentMethod: PaymentMethod = 'instant'
  ): Promise<string> {
    // Check monthly penalty limit
    const user = await UserService.getUser(userId);
    if (!user) {
      throw new FeeFenceError('User not found', 'USER_NOT_FOUND');
    }

    const currentMonthPenalties = await this.getMonthlyPenalties(userId);
    const currentMonthTotal = currentMonthPenalties.reduce((sum, p) => sum + p.amount, 0);
    
    if (currentMonthTotal + amount > user.settings.maxMonthlyPenalty) {
      throw new FeeFenceError(
        `Monthly penalty limit of $${user.settings.maxMonthlyPenalty} would be exceeded`,
        'MONTHLY_PENALTY_LIMIT_EXCEEDED'
      );
    }

    const penaltyData: Omit<Penalty, 'id' | 'createdAt' | 'updatedAt'> = {
      userId,
      blockedAppId,
      appName,
      amount,
      timestamp: new Date(),
      status: 'pending',
      paymentMethod,
    };

    // Set due date for 30-day payments
    if (paymentMethod === '30day') {
      const dueDate = new Date();
      dueDate.setDate(dueDate.getDate() + 30);
      penaltyData.dueDate = dueDate;
    }

    try {
      const id = await FirestoreService.create<Penalty>(
        COLLECTIONS.PENALTIES,
        penaltyData
      );

      return id;
    } catch (error) {
      throw new FeeFenceError(
        'Failed to create penalty',
        'PENALTY_CREATE_FAILED',
        error
      );
    }
  }

  // Get penalty by ID
  static async getPenalty(id: string): Promise<Penalty | null> {
    return FirestoreService.getById<Penalty>(COLLECTIONS.PENALTIES, id);
  }

  // Get all penalties for a user
  static async getUserPenalties(userId: string): Promise<Penalty[]> {
    return FirestoreService.query<Penalty>(COLLECTIONS.PENALTIES, [
      where('userId', '==', userId),
      orderBy('timestamp', 'desc'),
    ]);
  }

  // Get pending penalties for a user
  static async getPendingPenalties(userId: string): Promise<Penalty[]> {
    return FirestoreService.query<Penalty>(COLLECTIONS.PENALTIES, [
      where('userId', '==', userId),
      where('status', '==', 'pending'),
      orderBy('timestamp', 'desc'),
    ]);
  }

  // Get paid penalties for a user
  static async getPaidPenalties(userId: string): Promise<Penalty[]> {
    return FirestoreService.query<Penalty>(COLLECTIONS.PENALTIES, [
      where('userId', '==', userId),
      where('status', '==', 'paid'),
      orderBy('paidAt', 'desc'),
    ]);
  }

  // Get overdue penalties
  static async getOverduePenalties(userId: string): Promise<Penalty[]> {
    const now = new Date();
    const penalties = await FirestoreService.query<Penalty>(COLLECTIONS.PENALTIES, [
      where('userId', '==', userId),
      where('status', '==', 'pending'),
      where('paymentMethod', '==', '30day'),
    ]);

    return penalties.filter(penalty => 
      penalty.dueDate && penalty.dueDate < now
    );
  }

  // Get current month penalties
  static async getMonthlyPenalties(userId: string): Promise<Penalty[]> {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

    const penalties = await this.getUserPenalties(userId);
    return penalties.filter(penalty => 
      penalty.timestamp >= startOfMonth && penalty.timestamp <= endOfMonth
    );
  }

  // Update penalty status
  static async updatePenaltyStatus(
    id: string,
    status: PenaltyStatus,
    paymentDetails?: PaymentDetails
  ): Promise<void> {
    const updates: Partial<Penalty> = { status };

    if (status === 'paid') {
      updates.paidAt = new Date();
      updates.paymentDetails = paymentDetails;
    }

    await FirestoreService.update<Penalty>(COLLECTIONS.PENALTIES, id, updates);

    // Update user stats if paid
    if (status === 'paid') {
      const penalty = await this.getPenalty(id);
      if (penalty) {
        await UserService.addPenaltyPaid(penalty.userId, penalty.amount);
      }
    }
  }

  // Pay penalty instantly
  static async payPenaltyInstantly(
    id: string,
    transactionId: string
  ): Promise<void> {
    const paymentDetails: PaymentDetails = {
      transactionId,
    };

    await this.updatePenaltyStatus(id, 'paid', paymentDetails);
  }

  // Pay penalty to charity
  static async payPenaltyToCharity(
    id: string,
    charityName: string,
    charityId: string,
    transactionId?: string
  ): Promise<void> {
    const paymentDetails: PaymentDetails = {
      charityName,
      charityId,
      transactionId,
    };

    await this.updatePenaltyStatus(id, 'paid', paymentDetails);
  }

  // Forgive penalty with guardian approval
  static async forgivePenaltyWithGuardian(
    id: string,
    guardianId: string
  ): Promise<void> {
    const paymentDetails: PaymentDetails = {
      guardianApproval: true,
      guardianId,
    };

    await this.updatePenaltyStatus(id, 'forgiven', paymentDetails);
  }

  // Use grace unlock (forgive penalty)
  static async useGraceUnlock(id: string, userId: string): Promise<void> {
    // Check if user has grace unlocks available
    await UserService.useGraceUnlock(userId);

    const paymentDetails: PaymentDetails = {
      guardianApproval: false,
    };

    await this.updatePenaltyStatus(id, 'forgiven', paymentDetails);
  }

  // Mark penalty as overdue
  static async markPenaltyOverdue(id: string): Promise<void> {
    await this.updatePenaltyStatus(id, 'overdue');
  }

  // Get penalty statistics for user
  static async getPenaltyStats(userId: string): Promise<{
    totalPending: number;
    totalPaid: number;
    totalOverdue: number;
    pendingAmount: number;
    paidAmount: number;
    overdueAmount: number;
    monthlyTotal: number;
  }> {
    const allPenalties = await this.getUserPenalties(userId);
    const monthlyPenalties = await this.getMonthlyPenalties(userId);

    const pending = allPenalties.filter(p => p.status === 'pending');
    const paid = allPenalties.filter(p => p.status === 'paid');
    const overdue = allPenalties.filter(p => p.status === 'overdue');

    return {
      totalPending: pending.length,
      totalPaid: paid.length,
      totalOverdue: overdue.length,
      pendingAmount: pending.reduce((sum, p) => sum + p.amount, 0),
      paidAmount: paid.reduce((sum, p) => sum + p.amount, 0),
      overdueAmount: overdue.reduce((sum, p) => sum + p.amount, 0),
      monthlyTotal: monthlyPenalties.reduce((sum, p) => sum + p.amount, 0),
    };
  }

  // Delete penalty
  static async deletePenalty(id: string): Promise<void> {
    return FirestoreService.delete(COLLECTIONS.PENALTIES, id);
  }

  // Subscribe to user penalties
  static subscribeToUserPenalties(
    userId: string,
    callback: (penalties: Penalty[]) => void,
    onError?: (error: Error) => void
  ): () => void {
    return FirestoreService.subscribeToQuery<Penalty>(
      COLLECTIONS.PENALTIES,
      [
        where('userId', '==', userId),
        orderBy('timestamp', 'desc'),
      ],
      callback,
      onError
    );
  }

  // Subscribe to pending penalties
  static subscribeToPendingPenalties(
    userId: string,
    callback: (penalties: Penalty[]) => void,
    onError?: (error: Error) => void
  ): () => void {
    return FirestoreService.subscribeToQuery<Penalty>(
      COLLECTIONS.PENALTIES,
      [
        where('userId', '==', userId),
        where('status', '==', 'pending'),
        orderBy('timestamp', 'desc'),
      ],
      callback,
      onError
    );
  }

  // Check for overdue penalties and update status
  static async checkAndUpdateOverduePenalties(userId: string): Promise<void> {
    const overduePenalties = await this.getOverduePenalties(userId);
    
    for (const penalty of overduePenalties) {
      await this.markPenaltyOverdue(penalty.id);
    }
  }
}

export default PenaltyService;
