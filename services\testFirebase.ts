import { db, auth } from './firebase';
import { collection, addDoc, getDocs, deleteDoc, doc } from 'firebase/firestore';
import { createUserWithEmailAndPassword, signInWithEmailAndPassword, signOut } from 'firebase/auth';

/**
 * Test Firebase connection and basic operations
 * Call this function to verify your Firebase setup is working
 */
export async function testFirebaseConnection(): Promise<{
  success: boolean;
  message: string;
  details?: any;
}> {
  try {
    console.log('🔥 Testing Firebase connection...');

    // Test 1: Firestore connection
    console.log('📊 Testing Firestore connection...');
    const testCollection = collection(db, 'test');
    
    // Add a test document
    const testDoc = await addDoc(testCollection, {
      message: 'Hello from FeeFence!',
      timestamp: new Date(),
      test: true,
    });
    
    console.log('✅ Test document created with ID:', testDoc.id);

    // Read the test document
    const snapshot = await getDocs(testCollection);
    const docs = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    console.log('✅ Test documents read:', docs.length);

    // Clean up - delete the test document
    await deleteDoc(doc(db, 'test', testDoc.id));
    console.log('✅ Test document cleaned up');

    // Test 2: Authentication (optional - only if you want to test auth)
    console.log('🔐 Firebase Auth is available');

    return {
      success: true,
      message: 'Firebase connection successful! 🎉',
      details: {
        firestore: 'Connected and working',
        auth: 'Available',
        projectId: 'feefence',
        docsCreated: 1,
        docsRead: docs.length,
      }
    };

  } catch (error: any) {
    console.error('❌ Firebase connection failed:', error);
    
    return {
      success: false,
      message: 'Firebase connection failed',
      details: {
        error: error.message,
        code: error.code,
        stack: error.stack,
      }
    };
  }
}

/**
 * Test user authentication flow
 * Only call this if you want to test auth with a real email
 */
export async function testAuthentication(
  email: string = '<EMAIL>',
  password: string = 'testpassword123'
): Promise<{
  success: boolean;
  message: string;
  details?: any;
}> {
  try {
    console.log('🔐 Testing Firebase Authentication...');

    // Test sign up
    console.log('📝 Testing user registration...');
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    console.log('✅ User created:', userCredential.user.uid);

    // Test sign out
    console.log('🚪 Testing sign out...');
    await signOut(auth);
    console.log('✅ User signed out');

    // Test sign in
    console.log('🔑 Testing sign in...');
    const signInCredential = await signInWithEmailAndPassword(auth, email, password);
    console.log('✅ User signed in:', signInCredential.user.uid);

    // Sign out again
    await signOut(auth);
    console.log('✅ Final sign out completed');

    return {
      success: true,
      message: 'Authentication test successful! 🎉',
      details: {
        userId: userCredential.user.uid,
        email: userCredential.user.email,
        operations: ['signup', 'signout', 'signin', 'signout'],
      }
    };

  } catch (error: any) {
    console.error('❌ Authentication test failed:', error);
    
    // Try to sign out in case we're stuck in a signed-in state
    try {
      await signOut(auth);
    } catch (signOutError) {
      console.log('Could not sign out:', signOutError);
    }

    return {
      success: false,
      message: 'Authentication test failed',
      details: {
        error: error.message,
        code: error.code,
      }
    };
  }
}

/**
 * Quick connection test - just checks if Firebase is initialized
 */
export function quickConnectionTest(): {
  success: boolean;
  message: string;
  details: any;
} {
  try {
    const hasDb = !!db;
    const hasAuth = !!auth;
    
    return {
      success: hasDb && hasAuth,
      message: hasDb && hasAuth ? 'Firebase initialized successfully' : 'Firebase initialization incomplete',
      details: {
        firestore: hasDb ? 'Initialized' : 'Not initialized',
        auth: hasAuth ? 'Initialized' : 'Not initialized',
        projectId: 'feefence',
      }
    };
  } catch (error: any) {
    return {
      success: false,
      message: 'Firebase initialization failed',
      details: {
        error: error.message,
      }
    };
  }
}

// Export for easy testing
export default {
  testFirebaseConnection,
  testAuthentication,
  quickConnectionTest,
};
