import { GoogleSignin, statusCodes } from '@react-native-google-signin/google-signin';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface GoogleUser {
  uid: string;
  email: string;
  displayName: string;
  photoURL?: string;
  idToken: string;
}

class GoogleAuthService {
  private initialized = false;

  async initialize() {
    if (this.initialized) return;

    try {
      GoogleSignin.configure({
        // You'll need to add your Google OAuth client ID here
        // Get it from Google Cloud Console
        webClientId: 'YOUR_WEB_CLIENT_ID.apps.googleusercontent.com',
        offlineAccess: true,
        hostedDomain: '',
        forceCodeForRefreshToken: true,
      });
      
      this.initialized = true;
      console.log('Google Sign-In initialized');
    } catch (error) {
      console.error('Google Sign-In initialization failed:', error);
      throw error;
    }
  }

  async signIn(): Promise<GoogleUser> {
    try {
      await this.initialize();
      
      // Check if device supports Google Play Services
      await GoogleSignin.hasPlayServices();
      
      // Sign in
      const userInfo = await GoogleSignin.signIn();
      
      if (!userInfo.user) {
        throw new Error('No user information received from Google');
      }

      const googleUser: GoogleUser = {
        uid: userInfo.user.id,
        email: userInfo.user.email,
        displayName: userInfo.user.name || userInfo.user.email,
        photoURL: userInfo.user.photo || undefined,
        idToken: userInfo.idToken || '',
      };

      // Store user data locally
      await AsyncStorage.setItem('@feefence_google_user', JSON.stringify(googleUser));
      
      console.log('Google sign-in successful:', googleUser.email);
      return googleUser;
      
    } catch (error: any) {
      console.error('Google sign-in error:', error);
      
      if (error.code === statusCodes.SIGN_IN_CANCELLED) {
        throw new Error('Sign-in was cancelled by user');
      } else if (error.code === statusCodes.IN_PROGRESS) {
        throw new Error('Sign-in is already in progress');
      } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
        throw new Error('Google Play Services not available');
      } else {
        throw new Error('Google sign-in failed. Please try again.');
      }
    }
  }

  async signOut(): Promise<void> {
    try {
      await GoogleSignin.signOut();
      await AsyncStorage.removeItem('@feefence_google_user');
      console.log('Google sign-out successful');
    } catch (error) {
      console.error('Google sign-out error:', error);
      throw new Error('Sign-out failed');
    }
  }

  async getCurrentUser(): Promise<GoogleUser | null> {
    try {
      // First check local storage
      const storedUser = await AsyncStorage.getItem('@feefence_google_user');
      if (storedUser) {
        return JSON.parse(storedUser);
      }

      // Then check Google Sign-In state
      await this.initialize();
      const isSignedIn = await GoogleSignin.isSignedIn();
      
      if (isSignedIn) {
        const userInfo = await GoogleSignin.getCurrentUser();
        if (userInfo?.user) {
          const googleUser: GoogleUser = {
            uid: userInfo.user.id,
            email: userInfo.user.email,
            displayName: userInfo.user.name || userInfo.user.email,
            photoURL: userInfo.user.photo || undefined,
            idToken: userInfo.idToken || '',
          };
          
          // Store for future use
          await AsyncStorage.setItem('@feefence_google_user', JSON.stringify(googleUser));
          return googleUser;
        }
      }
      
      return null;
    } catch (error) {
      console.error('Error getting current Google user:', error);
      return null;
    }
  }

  async revokeAccess(): Promise<void> {
    try {
      await GoogleSignin.revokeAccess();
      await AsyncStorage.removeItem('@feefence_google_user');
      console.log('Google access revoked');
    } catch (error) {
      console.error('Error revoking Google access:', error);
      throw new Error('Failed to revoke access');
    }
  }
}

export const googleAuthService = new GoogleAuthService();
export default googleAuthService;
