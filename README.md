# FeeFence - Financially Gamified Focus App 🔒💰

FeeFence is the world's first financially gamified focus app that helps you stay focused by locking apps with financial penalties.

This is an [Expo](https://expo.dev) project with Firebase backend integration.

## 🚀 Quick Start

### 1. Install dependencies
```bash
npm install
```

### 2. Set up Firebase
1. Follow the detailed setup guide in [`firebase-setup.md`](./firebase-setup.md)
2. Update your Firebase config in `services/firebase.ts`

### 3. Start the app
```bash
npx expo start
```

In the output, you'll find options to open the app in a

- [development build](https://docs.expo.dev/develop/development-builds/introduction/)
- [Android emulator](https://docs.expo.dev/workflow/android-studio-emulator/)
- [iOS simulator](https://docs.expo.dev/workflow/ios-simulator/)
- [Expo Go](https://expo.dev/go), a limited sandbox for trying out app development with Expo

## 🏗️ Project Structure

```
FeeFence/
├── app/                    # Expo Router screens
│   ├── (tabs)/            # Tab navigation screens
│   ├── AuthContext.tsx    # Authentication context
│   ├── ThemeContext.tsx   # Theme management
│   └── LanguageContext.tsx # Internationalization
├── services/              # Firebase services
│   ├── firebase.ts        # Firebase configuration
│   ├── firestore.ts       # Firestore operations
│   ├── userService.ts     # User management
│   ├── blockedAppService.ts # App blocking logic
│   └── penaltyService.ts  # Penalty management
├── hooks/                 # Custom React hooks
│   ├── useAuth.ts         # Authentication hook
│   ├── useBlockedApps.ts  # Blocked apps hook
│   └── usePenalties.ts    # Penalties hook
├── types/                 # TypeScript type definitions
├── components/            # Reusable UI components
├── constants/             # App constants
├── utils/                 # Utility functions
└── i18n/                  # Internationalization
```

## 🎯 Core Features

- **App Locking**: Block distracting apps with financial penalties
- **Payment Options**: Instant payment, 30-day payment, or charity donation
- **Guardian System**: Friends/family can provide unlock codes
- **Grace Unlocks**: 2 free unlocks per month
- **Multi-language**: Support for 12 languages
- **Themes**: Super Black (dark) and Boxy Beige (light) themes
- **Real-time Sync**: Firebase Firestore for live updates

You can start developing by editing the files inside the **app** directory. This project uses [file-based routing](https://docs.expo.dev/router/introduction).

## Get a fresh project

When you're ready, run:

```bash
npm run reset-project
```

This command will move the starter code to the **app-example** directory and create a blank **app** directory where you can start developing.

## Learn more

To learn more about developing your project with Expo, look at the following resources:

- [Expo documentation](https://docs.expo.dev/): Learn fundamentals, or go into advanced topics with our [guides](https://docs.expo.dev/guides).
- [Learn Expo tutorial](https://docs.expo.dev/tutorial/introduction/): Follow a step-by-step tutorial where you'll create a project that runs on Android, iOS, and the web.

## Join the community

Join our community of developers creating universal apps.

- [Expo on GitHub](https://github.com/expo/expo): View our open source platform and contribute.
- [Discord community](https://chat.expo.dev): Chat with Expo users and ask questions.
