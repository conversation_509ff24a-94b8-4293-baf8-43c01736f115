import { useEffect, useState } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface MockUser {
  uid: string;
  email: string;
  displayName: string;
}

interface AuthState {
  user: MockUser | null;
  loading: boolean;
  error: string | null;
}

const STORAGE_KEY = '@feefence_user';

export function useMockAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    loading: true,
    error: null,
  });

  useEffect(() => {
    // Check for stored user on app start
    checkStoredUser();
  }, []);

  const checkStoredUser = async () => {
    try {
      const storedUser = await AsyncStorage.getItem(STORAGE_KEY);
      if (storedUser) {
        const user = JSON.parse(storedUser);
        setAuthState({
          user,
          loading: false,
          error: null,
        });
      } else {
        setAuthState({
          user: null,
          loading: false,
          error: null,
        });
      }
    } catch (error) {
      console.error('Error checking stored user:', error);
      setAuthState({
        user: null,
        loading: false,
        error: 'Failed to check stored user',
      });
    }
  };

  const signIn = async (email: string, password: string): Promise<void> => {
    setAuthState(prev => ({ ...prev, loading: true, error: null }));
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock validation
    if (!email || !password) {
      setAuthState(prev => ({
        ...prev,
        loading: false,
        error: 'Please fill in all fields',
      }));
      throw new Error('Please fill in all fields');
    }

    if (password.length < 6) {
      setAuthState(prev => ({
        ...prev,
        loading: false,
        error: 'Password must be at least 6 characters',
      }));
      throw new Error('Password must be at least 6 characters');
    }

    // Create mock user
    const user: MockUser = {
      uid: `mock_${Date.now()}`,
      email,
      displayName: email.split('@')[0],
    };

    // Store user
    await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(user));
    
    setAuthState({
      user,
      loading: false,
      error: null,
    });
  };

  const signUp = async (
    email: string,
    password: string,
    displayName: string
  ): Promise<void> => {
    setAuthState(prev => ({ ...prev, loading: true, error: null }));
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Mock validation
    if (!email || !password || !displayName) {
      setAuthState(prev => ({
        ...prev,
        loading: false,
        error: 'Please fill in all fields',
      }));
      throw new Error('Please fill in all fields');
    }

    if (password.length < 6) {
      setAuthState(prev => ({
        ...prev,
        loading: false,
        error: 'Password must be at least 6 characters',
      }));
      throw new Error('Password must be at least 6 characters');
    }

    // Create mock user
    const user: MockUser = {
      uid: `mock_${Date.now()}`,
      email,
      displayName,
    };

    // Store user
    await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(user));
    
    setAuthState({
      user,
      loading: false,
      error: null,
    });
  };

  const signOut = async (): Promise<void> => {
    setAuthState(prev => ({ ...prev, loading: true, error: null }));
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Remove stored user
    await AsyncStorage.removeItem(STORAGE_KEY);
    
    setAuthState({
      user: null,
      loading: false,
      error: null,
    });
  };

  return {
    user: authState.user,
    loading: authState.loading,
    error: authState.error,
    signIn,
    signUp,
    signOut,
    isAuthenticated: !!authState.user,
  };
}

export default useMockAuth;
