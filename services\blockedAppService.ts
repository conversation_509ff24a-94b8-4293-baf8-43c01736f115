import { where, orderBy, limit } from 'firebase/firestore';
import FirestoreService from './firestore';
import { BlockedApp, COLLECTIONS, FeeFenceError } from '@/types';
import UserService from './userService';

export class BlockedAppService {
  // Create a new blocked app
  static async createBlockedApp(
    userId: string,
    appName: string,
    packageName: string,
    bundleId: string,
    penaltyAmount: number,
    blockDuration: number, // in minutes
    iconUrl?: string
  ): Promise<string> {
    const blockedAppData: Omit<BlockedApp, 'id' | 'createdAt' | 'updatedAt'> = {
      userId,
      appName,
      packageName,
      bundleId,
      iconUrl,
      penaltyAmount,
      isActive: true,
      blockStartTime: new Date(),
      blockDuration,
      unlockAttempts: 0,
      successfulUnlocks: 0,
      totalTimeBlocked: 0,
    };

    try {
      const id = await FirestoreService.create<BlockedApp>(
        COLLECTIONS.BLOCKED_APPS,
        blockedAppData
      );

      // Update user stats
      const user = await UserService.getUser(userId);
      if (user) {
        await UserService.updateUserStats(userId, {
          appsBlocked: user.stats.appsBlocked + 1,
        });
      }

      return id;
    } catch (error) {
      throw new FeeFenceError(
        'Failed to create blocked app',
        'BLOCKED_APP_CREATE_FAILED',
        error
      );
    }
  }

  // Get blocked app by ID
  static async getBlockedApp(id: string): Promise<BlockedApp | null> {
    return FirestoreService.getById<BlockedApp>(COLLECTIONS.BLOCKED_APPS, id);
  }

  // Get all blocked apps for a user
  static async getUserBlockedApps(userId: string): Promise<BlockedApp[]> {
    return FirestoreService.query<BlockedApp>(COLLECTIONS.BLOCKED_APPS, [
      where('userId', '==', userId),
      orderBy('createdAt', 'desc'),
    ]);
  }

  // Get active blocked apps for a user
  static async getActiveBlockedApps(userId: string): Promise<BlockedApp[]> {
    return FirestoreService.query<BlockedApp>(COLLECTIONS.BLOCKED_APPS, [
      where('userId', '==', userId),
      where('isActive', '==', true),
      orderBy('createdAt', 'desc'),
    ]);
  }

  // Update blocked app
  static async updateBlockedApp(
    id: string,
    updates: Partial<Omit<BlockedApp, 'id' | 'createdAt' | 'updatedAt'>>
  ): Promise<void> {
    return FirestoreService.update<BlockedApp>(COLLECTIONS.BLOCKED_APPS, id, updates);
  }

  // Activate/Deactivate blocked app
  static async toggleBlockedApp(id: string, isActive: boolean): Promise<void> {
    const updates: Partial<BlockedApp> = {
      isActive,
    };

    if (isActive) {
      updates.blockStartTime = new Date();
    } else {
      updates.blockEndTime = new Date();
    }

    return this.updateBlockedApp(id, updates);
  }

  // Record unlock attempt
  static async recordUnlockAttempt(id: string): Promise<void> {
    const blockedApp = await this.getBlockedApp(id);
    if (!blockedApp) {
      throw new FeeFenceError('Blocked app not found', 'BLOCKED_APP_NOT_FOUND');
    }

    const unlockAttempts = blockedApp.unlockAttempts + 1;
    return this.updateBlockedApp(id, { unlockAttempts });
  }

  // Record successful unlock
  static async recordSuccessfulUnlock(id: string): Promise<void> {
    const blockedApp = await this.getBlockedApp(id);
    if (!blockedApp) {
      throw new FeeFenceError('Blocked app not found', 'BLOCKED_APP_NOT_FOUND');
    }

    const successfulUnlocks = blockedApp.successfulUnlocks + 1;
    await this.updateBlockedApp(id, { successfulUnlocks });

    // Update user stats
    const user = await UserService.getUser(blockedApp.userId);
    if (user) {
      await UserService.updateUserStats(blockedApp.userId, {
        successfulBlocks: user.stats.successfulBlocks + 1,
      });
      
      // Recalculate efficiency
      await UserService.calculateEfficiency(blockedApp.userId);
    }
  }

  // Update penalty amount
  static async updatePenaltyAmount(id: string, newAmount: number): Promise<void> {
    if (newAmount < 0) {
      throw new FeeFenceError('Penalty amount cannot be negative', 'INVALID_PENALTY_AMOUNT');
    }

    return this.updateBlockedApp(id, { penaltyAmount: newAmount });
  }

  // Update block duration
  static async updateBlockDuration(id: string, newDuration: number): Promise<void> {
    if (newDuration <= 0) {
      throw new FeeFenceError('Block duration must be positive', 'INVALID_BLOCK_DURATION');
    }

    return this.updateBlockedApp(id, { blockDuration: newDuration });
  }

  // Add time to total blocked time
  static async addBlockedTime(id: string, minutes: number): Promise<void> {
    const blockedApp = await this.getBlockedApp(id);
    if (!blockedApp) {
      throw new FeeFenceError('Blocked app not found', 'BLOCKED_APP_NOT_FOUND');
    }

    const totalTimeBlocked = blockedApp.totalTimeBlocked + minutes;
    await this.updateBlockedApp(id, { totalTimeBlocked });

    // Convert minutes to hours and update user stats
    const hours = minutes / 60;
    await UserService.addFocusHours(blockedApp.userId, hours);
  }

  // Check if app is currently blocked
  static async isAppBlocked(userId: string, packageName: string, bundleId: string): Promise<boolean> {
    const blockedApps = await FirestoreService.query<BlockedApp>(COLLECTIONS.BLOCKED_APPS, [
      where('userId', '==', userId),
      where('isActive', '==', true),
    ]);

    return blockedApps.some(app => 
      app.packageName === packageName || app.bundleId === bundleId
    );
  }

  // Get blocked app by package/bundle ID
  static async getBlockedAppByIdentifier(
    userId: string,
    packageName?: string,
    bundleId?: string
  ): Promise<BlockedApp | null> {
    const blockedApps = await this.getActiveBlockedApps(userId);
    
    return blockedApps.find(app => 
      (packageName && app.packageName === packageName) ||
      (bundleId && app.bundleId === bundleId)
    ) || null;
  }

  // Delete blocked app
  static async deleteBlockedApp(id: string): Promise<void> {
    const blockedApp = await this.getBlockedApp(id);
    if (!blockedApp) {
      throw new FeeFenceError('Blocked app not found', 'BLOCKED_APP_NOT_FOUND');
    }

    await FirestoreService.delete(COLLECTIONS.BLOCKED_APPS, id);

    // Update user stats
    const user = await UserService.getUser(blockedApp.userId);
    if (user && user.stats.appsBlocked > 0) {
      await UserService.updateUserStats(blockedApp.userId, {
        appsBlocked: user.stats.appsBlocked - 1,
      });
    }
  }

  // Get blocked apps summary for user
  static async getBlockedAppsSummary(userId: string): Promise<{
    total: number;
    active: number;
    totalPenaltyAmount: number;
    totalTimeBlocked: number;
  }> {
    const allApps = await this.getUserBlockedApps(userId);
    const activeApps = allApps.filter(app => app.isActive);
    
    const totalPenaltyAmount = activeApps.reduce((sum, app) => sum + app.penaltyAmount, 0);
    const totalTimeBlocked = allApps.reduce((sum, app) => sum + app.totalTimeBlocked, 0);

    return {
      total: allApps.length,
      active: activeApps.length,
      totalPenaltyAmount,
      totalTimeBlocked,
    };
  }

  // Subscribe to user's blocked apps
  static subscribeToUserBlockedApps(
    userId: string,
    callback: (apps: BlockedApp[]) => void,
    onError?: (error: Error) => void
  ): () => void {
    return FirestoreService.subscribeToQuery<BlockedApp>(
      COLLECTIONS.BLOCKED_APPS,
      [
        where('userId', '==', userId),
        orderBy('createdAt', 'desc'),
      ],
      callback,
      onError
    );
  }

  // Subscribe to active blocked apps
  static subscribeToActiveBlockedApps(
    userId: string,
    callback: (apps: BlockedApp[]) => void,
    onError?: (error: Error) => void
  ): () => void {
    return FirestoreService.subscribeToQuery<BlockedApp>(
      COLLECTIONS.BLOCKED_APPS,
      [
        where('userId', '==', userId),
        where('isActive', '==', true),
        orderBy('createdAt', 'desc'),
      ],
      callback,
      onError
    );
  }
}

export default BlockedAppService;
