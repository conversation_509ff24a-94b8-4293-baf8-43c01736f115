import React, { createContext, ReactNode, useContext } from 'react';
// Temporarily comment out Firebase imports
// import { useAuth } from '@/hooks/useAuth';
// import { User as FirebaseUser } from 'firebase/auth';
// import { User } from '@/types';

// Temporarily simplified context for demo
const AuthContext = createContext<{
  user: any | null;
  loading: boolean;
  isAuthenticated: boolean;
} | null>(null);

// Props for the provider
interface AuthProviderProps {
  children: ReactNode;
}

// Auth provider component
export function AuthProvider({ children }: AuthProviderProps) {
  // Mock auth state for demo
  const mockAuth = {
    user: null,
    loading: false,
    isAuthenticated: false,
  };

  return (
    <AuthContext.Provider value={mockAuth}>
      {children}
    </AuthContext.Provider>
  );
}

// Custom hook to use auth context
export function useAuthContext() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuthContext must be used within an AuthProvider');
  }
  return context;
}

export default AuthProvider;
