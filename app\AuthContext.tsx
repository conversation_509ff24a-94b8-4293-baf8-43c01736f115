import useMockAuth from '@/hooks/useMockAuth';
import React, { createContext, ReactNode, useContext } from 'react';

// Mock user type for now
interface MockUser {
  uid: string;
  email: string;
  displayName: string;
}

// Auth context type
const AuthContext = createContext<{
  user: MockUser | null;
  loading: boolean;
  isAuthenticated: boolean;
  signOut: () => Promise<void>;
} | null>(null);

// Props for the provider
interface AuthProviderProps {
  children: ReactNode;
}

// Auth provider component
export function AuthProvider({ children }: AuthProviderProps) {
  const mockAuth = useMockAuth();
  const { user, loading, signOut } = mockAuth;

  const authValue = {
    user,
    loading,
    isAuthenticated: !!user,
    signOut,
  };

  return (
    <AuthContext.Provider value={authValue}>
      {children}
    </AuthContext.Provider>
  );
}

// Custom hook to use auth context
export function useAuthContext() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuthContext must be used within an AuthProvider');
  }
  return context;
}

export default AuthProvider;
