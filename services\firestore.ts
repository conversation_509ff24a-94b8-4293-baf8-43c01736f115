import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  onSnapshot,
  serverTimestamp,
  Timestamp,
  DocumentReference,
  QueryConstraint,
} from 'firebase/firestore';
import { db } from './firebase';
import { 
  User, 
  BlockedApp, 
  Penalty, 
  GuardianCode, 
  FocusSession, 
  Achievement,
  COLLECTIONS,
  FeeFenceError,
  ApiResponse 
} from '@/types';

// Generic Firestore operations
export class FirestoreService {
  // Create a document
  static async create<T>(
    collectionName: string, 
    data: Omit<T, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<string> {
    try {
      const docRef = await addDoc(collection(db, collectionName), {
        ...data,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      });
      return docRef.id;
    } catch (error) {
      console.error(`Error creating document in ${collectionName}:`, error);
      throw new FeeFenceError(
        `Failed to create ${collectionName}`,
        'CREATE_FAILED',
        error
      );
    }
  }

  // Get a document by ID
  static async getById<T>(
    collectionName: string, 
    id: string
  ): Promise<T | null> {
    try {
      const docRef = doc(db, collectionName, id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return {
          id: docSnap.id,
          ...docSnap.data(),
          createdAt: docSnap.data().createdAt?.toDate(),
          updatedAt: docSnap.data().updatedAt?.toDate(),
        } as T;
      }
      return null;
    } catch (error) {
      console.error(`Error getting document ${id} from ${collectionName}:`, error);
      throw new FeeFenceError(
        `Failed to get ${collectionName}`,
        'GET_FAILED',
        error
      );
    }
  }

  // Update a document
  static async update<T>(
    collectionName: string,
    id: string,
    data: Partial<Omit<T, 'id' | 'createdAt' | 'updatedAt'>>
  ): Promise<void> {
    try {
      const docRef = doc(db, collectionName, id);
      await updateDoc(docRef, {
        ...data,
        updatedAt: serverTimestamp(),
      });
    } catch (error) {
      console.error(`Error updating document ${id} in ${collectionName}:`, error);
      throw new FeeFenceError(
        `Failed to update ${collectionName}`,
        'UPDATE_FAILED',
        error
      );
    }
  }

  // Delete a document
  static async delete(collectionName: string, id: string): Promise<void> {
    try {
      const docRef = doc(db, collectionName, id);
      await deleteDoc(docRef);
    } catch (error) {
      console.error(`Error deleting document ${id} from ${collectionName}:`, error);
      throw new FeeFenceError(
        `Failed to delete ${collectionName}`,
        'DELETE_FAILED',
        error
      );
    }
  }

  // Query documents
  static async query<T>(
    collectionName: string,
    constraints: QueryConstraint[] = []
  ): Promise<T[]> {
    try {
      const q = query(collection(db, collectionName), ...constraints);
      const querySnapshot = await getDocs(q);
      
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate(),
        updatedAt: doc.data().updatedAt?.toDate(),
      })) as T[];
    } catch (error) {
      console.error(`Error querying ${collectionName}:`, error);
      throw new FeeFenceError(
        `Failed to query ${collectionName}`,
        'QUERY_FAILED',
        error
      );
    }
  }

  // Real-time listener
  static subscribeToQuery<T>(
    collectionName: string,
    constraints: QueryConstraint[],
    callback: (data: T[]) => void,
    onError?: (error: Error) => void
  ): () => void {
    const q = query(collection(db, collectionName), ...constraints);
    
    return onSnapshot(
      q,
      (querySnapshot) => {
        const data = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate(),
          updatedAt: doc.data().updatedAt?.toDate(),
        })) as T[];
        callback(data);
      },
      (error) => {
        console.error(`Error in real-time listener for ${collectionName}:`, error);
        if (onError) {
          onError(new FeeFenceError(
            `Real-time listener failed for ${collectionName}`,
            'LISTENER_FAILED',
            error
          ));
        }
      }
    );
  }

  // Subscribe to a single document
  static subscribeToDocument<T>(
    collectionName: string,
    id: string,
    callback: (data: T | null) => void,
    onError?: (error: Error) => void
  ): () => void {
    const docRef = doc(db, collectionName, id);
    
    return onSnapshot(
      docRef,
      (docSnap) => {
        if (docSnap.exists()) {
          const data = {
            id: docSnap.id,
            ...docSnap.data(),
            createdAt: docSnap.data().createdAt?.toDate(),
            updatedAt: docSnap.data().updatedAt?.toDate(),
          } as T;
          callback(data);
        } else {
          callback(null);
        }
      },
      (error) => {
        console.error(`Error in document listener for ${collectionName}/${id}:`, error);
        if (onError) {
          onError(new FeeFenceError(
            `Document listener failed for ${collectionName}/${id}`,
            'LISTENER_FAILED',
            error
          ));
        }
      }
    );
  }
}

// Helper function to convert Firestore Timestamp to Date
export const timestampToDate = (timestamp: Timestamp | Date): Date => {
  if (timestamp instanceof Date) {
    return timestamp;
  }
  return timestamp.toDate();
};

// Helper function to create server timestamp
export const createServerTimestamp = () => serverTimestamp();

// Batch operations helper
export const createBatch = () => {
  // Note: Import writeBatch from firebase/firestore when needed
  // return writeBatch(db);
};

export default FirestoreService;
