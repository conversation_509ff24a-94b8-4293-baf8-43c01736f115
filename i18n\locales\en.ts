// English translations
export default {
  // Common words
  common: {
    ok: 'OK',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    back: 'Back',
    next: 'Next',
    settings: 'Settings',
    loading: 'Loading...',
  },

  // Page subtitles
  subtitles: {
    settings: 'Customize Your Experience',
    dashboard: 'Neural Analytics Dashboard',
    fees: 'Fee Management Center',
    lock: 'App Restriction Center',
  },

  // Tab navigation
  tabs: {
    home: 'Home',
    lock: 'Lock',
    fees: 'Fees',
    dashboard: 'Dashboard',
    explore: 'Explore',
    settings: 'Settings',
  },

  // Welcome / Home screen
  home: {
    welcome: 'Welcome!',
    step1Title: 'Step 1: Try it',
    step2Title: 'Step 2: Explore',
    step3Title: 'Step 3: Get a fresh start',
  },

  // Lock/Unlock page
  lock: {
    title: 'App Lock',
    masterLock: 'Master Lock',
    masterLockDesc: 'Enable to activate app blocking and penalty system',
    masterLockTitle: 'Master Lock',
    masterLockDisableWarning: 'Disabling master lock will turn off all app blocking. Are you sure?',
    masterLockEnableConfirm: 'Enable master lock to start blocking apps?',
    masterLockRequired: 'Master Lock Required',
    masterLockRequiredDesc: 'Please enable master lock first to manage individual apps.',
    blockedApps: 'Blocked Apps',
    totalPenalty: 'Total Penalty',
    manageApps: 'Manage Apps',
    manageAppsDesc: 'Toggle app blocking and set penalty amounts',
    blockApp: 'Block App',
    unblockApp: 'Unblock App',
    blockAppConfirm: 'Block {{app}}? You will be charged {{amount}} for each violation.',
    unblockAppWarning: 'Unblock {{app}}? This will remove the {{amount}} penalty.',
    penaltyAmount: '{{amount}} penalty',
    blocked: 'Blocked',
    unblocked: 'Unblocked',
  },

  // Fees page
  fees: {
    title: 'Fees',
    pendingFees: 'Pending',
    paidFees: 'Paid',
    payAllFees: 'Pay All ({{total}})',
    payFeeTitle: 'Pay Fee',
    payFeeConfirm: 'Pay {{amount}} fee for {{app}}?',
    payNow: 'Pay Now',
    payAll: 'Pay All',
    paymentSuccess: 'Payment Successful',
    paymentSuccessDesc: 'Your fee has been paid successfully.',
    allFeesPaymentSuccessDesc: 'All fees have been paid successfully.',
    payAllFeesTitle: 'Pay All Fees',
    payAllFeesConfirm: 'Pay all pending fees totaling {{total}}?',
    disputeFeeTitle: 'Dispute Fee',
    disputeFeeDesc: 'Submit a dispute for the {{app}} fee?',
    submitDispute: 'Submit Dispute',
    disputeSubmitted: 'Dispute Submitted',
    disputeSubmittedDesc: 'Your dispute has been submitted and will be reviewed.',
    dispute: 'Dispute',
    pending: 'Pending',
    paid: 'Paid',
    usageDuration: '{{duration}} minutes',
    paidOn: 'Paid on {{date}}',
    noPendingFees: 'No Pending Fees',
    noPendingFeesDesc: 'Great job! You have no pending fees.',
  },

  // Dashboard
  dashboard: {
    title: 'Dashboard',
    subtitle: 'Neural Analytics Dashboard',
    dayStreak: 'Days',
    focusHours: 'Hours',
    moneySaved: 'Saved',
    blocksToday: 'Blocks Today',
    recentActivity: 'Recent Activity',
    achievements: 'Neural Achievements',
    viewFullAnalytics: 'View Full Analytics',
    achievementFocusWarrior: 'Focus Warrior',
    achievementFocusWarriorDesc: '{{streak}} day streak',
    achievementMoneySaver: 'Money Saver',
    achievementMoneySaverDesc: 'Saved {{amount}}',
    achievementDigitalMinimalist: 'Digital Minimalist',
    achievementDigitalMinimalistDesc: 'Coming soon...',
  },

  // Settings
  settings: {
    title: 'Settings',
    theme: 'Theme',
    themeDescription: 'Choose your preferred appearance',
    language: 'Language',
    languageDescription: 'Choose your preferred language',
    glowEffects: 'Glow Effects',
    glowEffectsDescription: 'Control the intensity of neon glow effects in dark mode',
    enhancedGlow: 'Enhanced Glow',
    enhancedGlowDescription: 'Enable ambient glow effects throughout the interface'
  },

  // Theme names
  themes: {
    system: 'System Default',
    systemDesc: 'Follow system light/dark settings',
    superBlack: 'Super Black',
    superBlackDesc: 'Dark mode with true black background and silver accents',
    boxyBeige: 'Boxy Beige',
    boxyBeigeDesc: 'Light mode with warm beige colors and brown highlights',
  },

  // Authentication
  auth: {
    signIn: 'Sign In',
    signUp: 'Sign Up',
    signOut: 'Sign Out',
    email: 'Email',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    fullName: 'Full Name',
    emailPlaceholder: 'Enter your email',
    passwordPlaceholder: 'Enter your password',
    confirmPasswordPlaceholder: 'Confirm your password',
    fullNamePlaceholder: 'Enter your full name',
    createAccount: 'Create Account',
    getStarted: 'Get Started',
    noAccount: "Don't have an account?",
    haveAccount: 'Already have an account?',
    fillAllFields: 'Please fill in all fields',
    nameRequired: 'Name is required',
    emailRequired: 'Email is required',
    passwordTooShort: 'Password must be at least 6 characters',
    passwordsDoNotMatch: 'Passwords do not match',
    loginFailed: 'Login failed. Please try again.',
    registrationFailed: 'Registration failed. Please try again.',
    userNotFound: 'No account found with this email',
    wrongPassword: 'Incorrect password',
    invalidEmail: 'Invalid email address',
    emailAlreadyInUse: 'An account with this email already exists',
    weakPassword: 'Password is too weak',
    tooManyRequests: 'Too many attempts. Please try again later.',
    networkError: 'Network error. Please check your connection.',
    error: 'Error',
    success: 'Success',
    accountCreated: 'Account created successfully! Welcome to FeeFence.',
  },

  // Welcome screen
  welcome: {
    tagline: 'Take control of your digital habits with smart app blocking and penalty fees',
    feature1: 'Smart app blocking with customizable penalties',
    feature2: 'Real-time analytics and progress tracking',
    feature3: 'Instant notifications and achievement system',
    footer: 'Join thousands of users who have improved their digital wellness with FeeFence',
  },
};