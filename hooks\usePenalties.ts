import { useEffect, useState, useCallback } from 'react';
import { Penalty, PaymentMethod, FeeFenceError } from '@/types';
import PenaltyService from '@/services/penaltyService';
import { useAuth } from './useAuth';

interface UsePenaltiesState {
  penalties: Penalty[];
  pendingPenalties: Penalty[];
  paidPenalties: Penalty[];
  loading: boolean;
  error: string | null;
  stats: {
    totalPending: number;
    totalPaid: number;
    totalOverdue: number;
    pendingAmount: number;
    paidAmount: number;
    overdueAmount: number;
    monthlyTotal: number;
  } | null;
}

export function usePenalties() {
  const { user } = useAuth();
  const [state, setState] = useState<UsePenaltiesState>({
    penalties: [],
    pendingPenalties: [],
    paidPenalties: [],
    loading: true,
    error: null,
    stats: null,
  });

  // Subscribe to penalties
  useEffect(() => {
    if (!user) {
      setState({
        penalties: [],
        pendingPenalties: [],
        paidPenalties: [],
        loading: false,
        error: null,
        stats: null,
      });
      return;
    }

    setState(prev => ({ ...prev, loading: true, error: null }));

    // Subscribe to all penalties
    const unsubscribeAll = PenaltyService.subscribeToUserPenalties(
      user.uid,
      (penalties) => {
        setState(prev => ({ 
          ...prev, 
          penalties,
          loading: false,
          error: null,
        }));
      },
      (error) => {
        setState(prev => ({ 
          ...prev, 
          loading: false,
          error: error.message,
        }));
      }
    );

    // Subscribe to pending penalties
    const unsubscribePending = PenaltyService.subscribeToPendingPenalties(
      user.uid,
      (pendingPenalties) => {
        setState(prev => ({ 
          ...prev, 
          pendingPenalties,
        }));
      },
      (error) => {
        console.error('Error subscribing to pending penalties:', error);
      }
    );

    return () => {
      unsubscribeAll();
      unsubscribePending();
    };
  }, [user]);

  // Load paid penalties and stats
  useEffect(() => {
    if (!user) return;

    const loadAdditionalData = async () => {
      try {
        const [paidPenalties, stats] = await Promise.all([
          PenaltyService.getPaidPenalties(user.uid),
          PenaltyService.getPenaltyStats(user.uid),
        ]);

        setState(prev => ({ 
          ...prev, 
          paidPenalties,
          stats,
        }));
      } catch (error) {
        console.error('Error loading additional penalty data:', error);
      }
    };

    loadAdditionalData();
  }, [user, state.penalties.length]); // Reload when penalties change

  // Create a new penalty
  const createPenalty = useCallback(async (
    blockedAppId: string,
    appName: string,
    amount: number,
    paymentMethod: PaymentMethod = 'instant'
  ): Promise<string> => {
    if (!user) {
      throw new FeeFenceError('User not authenticated', 'AUTH_REQUIRED');
    }

    try {
      const id = await PenaltyService.createPenalty(
        user.uid,
        blockedAppId,
        appName,
        amount,
        paymentMethod
      );
      return id;
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to create penalty',
      }));
      throw error;
    }
  }, [user]);

  // Pay penalty instantly
  const payPenaltyInstantly = useCallback(async (
    penaltyId: string,
    transactionId: string
  ): Promise<void> => {
    try {
      await PenaltyService.payPenaltyInstantly(penaltyId, transactionId);
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to pay penalty',
      }));
      throw error;
    }
  }, []);

  // Pay penalty to charity
  const payPenaltyToCharity = useCallback(async (
    penaltyId: string,
    charityName: string,
    charityId: string,
    transactionId?: string
  ): Promise<void> => {
    try {
      await PenaltyService.payPenaltyToCharity(
        penaltyId,
        charityName,
        charityId,
        transactionId
      );
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to pay penalty to charity',
      }));
      throw error;
    }
  }, []);

  // Forgive penalty with guardian
  const forgivePenaltyWithGuardian = useCallback(async (
    penaltyId: string,
    guardianId: string
  ): Promise<void> => {
    try {
      await PenaltyService.forgivePenaltyWithGuardian(penaltyId, guardianId);
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to forgive penalty',
      }));
      throw error;
    }
  }, []);

  // Use grace unlock
  const useGraceUnlock = useCallback(async (penaltyId: string): Promise<void> => {
    if (!user) {
      throw new FeeFenceError('User not authenticated', 'AUTH_REQUIRED');
    }

    try {
      await PenaltyService.useGraceUnlock(penaltyId, user.uid);
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to use grace unlock',
      }));
      throw error;
    }
  }, [user]);

  // Get overdue penalties
  const getOverduePenalties = useCallback(async (): Promise<Penalty[]> => {
    if (!user) return [];

    try {
      return await PenaltyService.getOverduePenalties(user.uid);
    } catch (error) {
      console.error('Error getting overdue penalties:', error);
      return [];
    }
  }, [user]);

  // Get monthly penalties
  const getMonthlyPenalties = useCallback(async (): Promise<Penalty[]> => {
    if (!user) return [];

    try {
      return await PenaltyService.getMonthlyPenalties(user.uid);
    } catch (error) {
      console.error('Error getting monthly penalties:', error);
      return [];
    }
  }, [user]);

  // Check and update overdue penalties
  const checkOverduePenalties = useCallback(async (): Promise<void> => {
    if (!user) return;

    try {
      await PenaltyService.checkAndUpdateOverduePenalties(user.uid);
    } catch (error) {
      console.error('Error checking overdue penalties:', error);
    }
  }, [user]);

  // Refresh stats
  const refreshStats = useCallback(async (): Promise<void> => {
    if (!user) return;

    try {
      const stats = await PenaltyService.getPenaltyStats(user.uid);
      setState(prev => ({ ...prev, stats }));
    } catch (error) {
      console.error('Error refreshing penalty stats:', error);
    }
  }, [user]);

  // Clear error
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    penalties: state.penalties,
    pendingPenalties: state.pendingPenalties,
    paidPenalties: state.paidPenalties,
    loading: state.loading,
    error: state.error,
    stats: state.stats,
    createPenalty,
    payPenaltyInstantly,
    payPenaltyToCharity,
    forgivePenaltyWithGuardian,
    useGraceUnlock,
    getOverduePenalties,
    getMonthlyPenalties,
    checkOverduePenalties,
    refreshStats,
    clearError,
  };
}

export default usePenalties;
