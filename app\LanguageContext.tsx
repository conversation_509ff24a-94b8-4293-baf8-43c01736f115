import React, { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import { Alert, NativeModules, Platform } from 'react-native';

import AsyncStorage from '@react-native-async-storage/async-storage';

import { DEFAULT_LANGUAGE, LanguageCode, LANGUAGES } from '@/constants/Languages';
import { changeLanguage } from '@/i18n';

// Storage key for language preference
const LANGUAGE_STORAGE_KEY = 'feefence_language';

// Type definition for our language context
type LanguageContextType = {
  language: LanguageCode;
  setLanguage: (language: LanguageCode) => Promise<void>;
};

// Create the context with default values
export const LanguageContext = createContext<LanguageContextType>({
  language: DEFAULT_LANGUAGE,
  setLanguage: async () => {},
});

// Props for our LanguageProvider component
type LanguageProviderProps = {
  children: ReactNode;
};

// Helper function to get device language
const getDeviceLanguage = (): LanguageCode => {
  try {
    // Get the device language
    let deviceLang: string | undefined;

    if (Platform.OS === 'ios') {
      deviceLang = NativeModules.SettingsManager?.settings?.AppleLocale ||
                   NativeModules.SettingsManager?.settings?.AppleLanguages?.[0];
    } else {
      deviceLang = NativeModules.I18nManager?.localeIdentifier;
    }

    // If we couldn't get device language, return default
    if (!deviceLang || typeof deviceLang !== 'string') {
      return DEFAULT_LANGUAGE;
    }

    // Simplify to just the language code (e.g. "en-US" -> "en")
    const languageCode = deviceLang.split('-')[0];

    // Check if the language is supported
    const supportedLanguages = LANGUAGES.map(lang => lang.code);

    return supportedLanguages.includes(languageCode as LanguageCode)
      ? languageCode as LanguageCode
      : DEFAULT_LANGUAGE;
  } catch (error) {
    console.warn('Failed to get device language:', error);
    return DEFAULT_LANGUAGE;
  }
};

// Language provider component to wrap the app
export function LanguageProvider({ children }: LanguageProviderProps) {
  const [language, setLanguageState] = useState<LanguageCode>(DEFAULT_LANGUAGE);
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize with stored language or device language
  useEffect(() => {
    const initializeLanguage = async () => {
      try {
        // Try to get stored language preference
        const storedLanguage = await AsyncStorage.getItem(LANGUAGE_STORAGE_KEY);

        // If no stored preference, use device language
        const initialLanguage = storedLanguage
          ? (storedLanguage as LanguageCode)
          : getDeviceLanguage();

        // Apply the language
        await changeLanguage(initialLanguage);
        setLanguageState(initialLanguage);
        setIsInitialized(true);
      } catch (error) {
        console.error('Failed to initialize language:', error);
        // Fallback to default language on error
        await changeLanguage(DEFAULT_LANGUAGE);
        setLanguageState(DEFAULT_LANGUAGE);
        setIsInitialized(true);
      }
    };

    initializeLanguage();
  }, []);

  // Function to set the language
  const setLanguage = async (newLanguage: LanguageCode) => {
    try {
      // Save preference to storage
      await AsyncStorage.setItem(LANGUAGE_STORAGE_KEY, newLanguage);

      // Apply the language change
      await changeLanguage(newLanguage);
      setLanguageState(newLanguage);
    } catch (error) {
      console.error('Failed to set language:', error);
      Alert.alert('Error', 'Failed to change language. Please try again.');
    }
  };

  // Wait until language is initialized before rendering children
  if (!isInitialized) {
    return null;
  }

  return (
    <LanguageContext.Provider value={{ language, setLanguage }}>
      {children}
    </LanguageContext.Provider>
  );
}

// Custom hook to use language
export function useLanguage() {
  return useContext(LanguageContext);
}

export default LanguageProvider;