import { where, orderBy, limit } from 'firebase/firestore';
import FirestoreService from './firestore';
import { User, UserSettings, UserStats, COLLECTIONS, FeeFenceError } from '@/types';
import { DEFAULT_LANGUAGE } from '@/constants/Languages';

export class UserService {
  // Create a new user
  static async createUser(
    id: string,
    email: string,
    displayName: string
  ): Promise<void> {
    const defaultSettings: UserSettings = {
      theme: 'system',
      language: DEFAULT_LANGUAGE,
      graceUnlocksUsed: 0,
      maxGraceUnlocksPerMonth: 2,
      maxMonthlyPenalty: 100, // $100
      glowEnabled: true,
      notificationsEnabled: true,
      guardianNotificationsEnabled: true,
    };

    const defaultStats: UserStats = {
      focusStreak: 0,
      totalFocusHours: 0,
      totalSaved: 0,
      totalPenaltiesPaid: 0,
      efficiency: 0,
      longestStreak: 0,
      appsBlocked: 0,
      successfulBlocks: 0,
    };

    const userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'> = {
      email,
      displayName,
      guardians: [],
      settings: defaultSettings,
      stats: defaultStats,
    };

    try {
      // Use the user's auth ID as the document ID
      await FirestoreService.create(COLLECTIONS.USERS, userData);
    } catch (error) {
      throw new FeeFenceError(
        'Failed to create user profile',
        'USER_CREATE_FAILED',
        error
      );
    }
  }

  // Get user by ID
  static async getUser(userId: string): Promise<User | null> {
    return FirestoreService.getById<User>(COLLECTIONS.USERS, userId);
  }

  // Update user profile
  static async updateUser(
    userId: string,
    updates: Partial<Omit<User, 'id' | 'createdAt' | 'updatedAt'>>
  ): Promise<void> {
    return FirestoreService.update<User>(COLLECTIONS.USERS, userId, updates);
  }

  // Update user settings
  static async updateUserSettings(
    userId: string,
    settings: Partial<UserSettings>
  ): Promise<void> {
    const user = await this.getUser(userId);
    if (!user) {
      throw new FeeFenceError('User not found', 'USER_NOT_FOUND');
    }

    const updatedSettings = { ...user.settings, ...settings };
    return this.updateUser(userId, { settings: updatedSettings });
  }

  // Update user stats
  static async updateUserStats(
    userId: string,
    stats: Partial<UserStats>
  ): Promise<void> {
    const user = await this.getUser(userId);
    if (!user) {
      throw new FeeFenceError('User not found', 'USER_NOT_FOUND');
    }

    const updatedStats = { ...user.stats, ...stats };
    return this.updateUser(userId, { stats: updatedStats });
  }

  // Add a guardian
  static async addGuardian(userId: string, guardianContact: string): Promise<void> {
    const user = await this.getUser(userId);
    if (!user) {
      throw new FeeFenceError('User not found', 'USER_NOT_FOUND');
    }

    if (user.guardians.includes(guardianContact)) {
      throw new FeeFenceError('Guardian already exists', 'GUARDIAN_EXISTS');
    }

    const updatedGuardians = [...user.guardians, guardianContact];
    return this.updateUser(userId, { guardians: updatedGuardians });
  }

  // Remove a guardian
  static async removeGuardian(userId: string, guardianContact: string): Promise<void> {
    const user = await this.getUser(userId);
    if (!user) {
      throw new FeeFenceError('User not found', 'USER_NOT_FOUND');
    }

    const updatedGuardians = user.guardians.filter(g => g !== guardianContact);
    return this.updateUser(userId, { guardians: updatedGuardians });
  }

  // Increment focus streak
  static async incrementFocusStreak(userId: string): Promise<void> {
    const user = await this.getUser(userId);
    if (!user) {
      throw new FeeFenceError('User not found', 'USER_NOT_FOUND');
    }

    const newStreak = user.stats.focusStreak + 1;
    const longestStreak = Math.max(user.stats.longestStreak, newStreak);

    return this.updateUserStats(userId, {
      focusStreak: newStreak,
      longestStreak,
    });
  }

  // Reset focus streak
  static async resetFocusStreak(userId: string): Promise<void> {
    return this.updateUserStats(userId, { focusStreak: 0 });
  }

  // Add focus hours
  static async addFocusHours(userId: string, hours: number): Promise<void> {
    const user = await this.getUser(userId);
    if (!user) {
      throw new FeeFenceError('User not found', 'USER_NOT_FOUND');
    }

    const totalFocusHours = user.stats.totalFocusHours + hours;
    return this.updateUserStats(userId, { totalFocusHours });
  }

  // Add money saved
  static async addMoneySaved(userId: string, amount: number): Promise<void> {
    const user = await this.getUser(userId);
    if (!user) {
      throw new FeeFenceError('User not found', 'USER_NOT_FOUND');
    }

    const totalSaved = user.stats.totalSaved + amount;
    return this.updateUserStats(userId, { totalSaved });
  }

  // Add penalty paid
  static async addPenaltyPaid(userId: string, amount: number): Promise<void> {
    const user = await this.getUser(userId);
    if (!user) {
      throw new FeeFenceError('User not found', 'USER_NOT_FOUND');
    }

    const totalPenaltiesPaid = user.stats.totalPenaltiesPaid + amount;
    return this.updateUserStats(userId, { totalPenaltiesPaid });
  }

  // Use grace unlock
  static async useGraceUnlock(userId: string): Promise<void> {
    const user = await this.getUser(userId);
    if (!user) {
      throw new FeeFenceError('User not found', 'USER_NOT_FOUND');
    }

    if (user.settings.graceUnlocksUsed >= user.settings.maxGraceUnlocksPerMonth) {
      throw new FeeFenceError(
        'Grace unlock limit exceeded',
        'GRACE_LIMIT_EXCEEDED'
      );
    }

    const graceUnlocksUsed = user.settings.graceUnlocksUsed + 1;
    return this.updateUserSettings(userId, { graceUnlocksUsed });
  }

  // Reset monthly grace unlocks (call this monthly)
  static async resetMonthlyGraceUnlocks(userId: string): Promise<void> {
    return this.updateUserSettings(userId, { graceUnlocksUsed: 0 });
  }

  // Calculate efficiency
  static async calculateEfficiency(userId: string): Promise<number> {
    const user = await this.getUser(userId);
    if (!user) {
      throw new FeeFenceError('User not found', 'USER_NOT_FOUND');
    }

    const { successfulBlocks, appsBlocked } = user.stats;
    if (appsBlocked === 0) return 0;

    const efficiency = Math.round((successfulBlocks / appsBlocked) * 100);
    await this.updateUserStats(userId, { efficiency });
    
    return efficiency;
  }

  // Subscribe to user updates
  static subscribeToUser(
    userId: string,
    callback: (user: User | null) => void,
    onError?: (error: Error) => void
  ): () => void {
    return FirestoreService.subscribeToDocument<User>(
      COLLECTIONS.USERS,
      userId,
      callback,
      onError
    );
  }

  // Delete user (GDPR compliance)
  static async deleteUser(userId: string): Promise<void> {
    // Note: This should also delete all related data (blocked apps, penalties, etc.)
    // Consider implementing a Cloud Function for this to ensure data consistency
    return FirestoreService.delete(COLLECTIONS.USERS, userId);
  }
}

export default UserService;
