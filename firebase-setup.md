# Firebase Setup for FeeFence

## 🔥 Firebase Project Setup

### 1. Create Firebase Project
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project"
3. Name it "FeeFence" or "feefence-app"
4. Enable Google Analytics (optional)
5. Create project

### 2. Enable Authentication
1. In Firebase Console, go to **Authentication** > **Sign-in method**
2. Enable **Email/Password** provider
3. Optionally enable **Google** and **Apple** for social login

### 3. Create Firestore Database
1. Go to **Firestore Database**
2. Click **Create database**
3. Choose **Start in test mode** (we'll add security rules later)
4. Select a location close to your users

### 4. Get Firebase Configuration
1. Go to **Project Settings** (gear icon)
2. Scroll down to **Your apps**
3. Click **Add app** > **Web** (</>) 
4. Register app with nickname "FeeFence Web"
5. Copy the configuration object

## 🔧 Configuration

### 1. Update Firebase Config
Replace the placeholder config in `services/firebase.ts`:

```typescript
const firebaseConfig = {
  apiKey: "your-actual-api-key",
  authDomain: "your-project-id.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project-id.appspot.com",
  messagingSenderId: "123456789",
  appId: "1:123456789:web:abcdef123456789",
  measurementId: "G-XXXXXXXXXX" // Optional
};
```

### 2. Install Dependencies
The Firebase SDK is already added to package.json. Run:

```bash
npm install
```

### 3. Environment Variables (Optional)
For better security, you can use environment variables:

Create `.env.local`:
```
EXPO_PUBLIC_FIREBASE_API_KEY=your-api-key
EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project-id.firebaseapp.com
EXPO_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project-id.appspot.com
EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=123456789
EXPO_PUBLIC_FIREBASE_APP_ID=1:123456789:web:abcdef123456789
```

Then update `services/firebase.ts`:
```typescript
const firebaseConfig = {
  apiKey: process.env.EXPO_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.EXPO_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.EXPO_PUBLIC_FIREBASE_APP_ID,
};
```

## 🔒 Security Rules

### Firestore Security Rules
Go to **Firestore Database** > **Rules** and replace with:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Blocked apps - users can only access their own
    match /blockedApps/{appId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
    }
    
    // Penalties - users can only access their own
    match /penalties/{penaltyId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
    }
    
    // Guardian codes - users can only access their own
    match /guardianCodes/{codeId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
    }
    
    // Focus sessions - users can only access their own
    match /focusSessions/{sessionId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
    }
    
    // Achievements - users can only access their own
    match /achievements/{achievementId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
    }
  }
}
```

## 🧪 Testing with Emulator (Optional)

### 1. Install Firebase CLI
```bash
npm install -g firebase-tools
```

### 2. Login and Initialize
```bash
firebase login
firebase init
```

Select:
- Firestore
- Authentication
- Functions (optional)

### 3. Start Emulators
```bash
firebase emulators:start
```

### 4. Enable Emulator in App
In `app.json`, change:
```json
"extra": {
  "useFirebaseEmulator": true
}
```

## 📱 Usage in App

### 1. Authentication
```typescript
import { useAuth } from '@/hooks/useAuth';

function LoginScreen() {
  const { signIn, signUp, loading, error } = useAuth();
  
  const handleLogin = async () => {
    try {
      await signIn(email, password);
    } catch (error) {
      console.error('Login failed:', error);
    }
  };
}
```

### 2. Blocked Apps
```typescript
import { useBlockedApps } from '@/hooks/useBlockedApps';

function LockScreen() {
  const { 
    blockedApps, 
    createBlockedApp, 
    toggleAppBlock 
  } = useBlockedApps();
  
  const handleCreateBlock = async () => {
    await createBlockedApp(
      'Instagram',
      'com.instagram.android',
      'com.burbn.instagram',
      5, // $5 penalty
      60 // 60 minutes
    );
  };
}
```

### 3. Penalties
```typescript
import { usePenalties } from '@/hooks/usePenalties';

function FeesScreen() {
  const { 
    pendingPenalties, 
    payPenaltyInstantly,
    useGraceUnlock 
  } = usePenalties();
}
```

## 🚀 Next Steps

1. **Set up Firebase project** following steps above
2. **Update configuration** in `services/firebase.ts`
3. **Deploy security rules** to Firestore
4. **Test authentication** flow
5. **Implement app blocking** logic
6. **Add payment processing** (Stripe/PayPal)
7. **Set up push notifications** (Firebase Cloud Messaging)

## 📚 Additional Resources

- [Firebase Documentation](https://firebase.google.com/docs)
- [Firestore Security Rules](https://firebase.google.com/docs/firestore/security/get-started)
- [Firebase Auth with React Native](https://firebase.google.com/docs/auth/web/start)
- [Expo Firebase Guide](https://docs.expo.dev/guides/using-firebase/)
