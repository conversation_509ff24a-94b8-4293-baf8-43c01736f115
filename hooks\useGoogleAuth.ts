import { auth } from '@/services/firebase';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { GoogleAuthProvider, signInWithCredential } from 'firebase/auth';
import { useEffect, useState } from 'react';

interface GoogleAuthState {
  isConfigured: boolean;
  isSigningIn: boolean;
  error: string | null;
}

export function useGoogleAuth() {
  const [state, setState] = useState<GoogleAuthState>({
    isConfigured: false,
    isSigningIn: false,
    error: null,
  });

  useEffect(() => {
    configureGoogleSignIn();
  }, []);

  const configureGoogleSignIn = async () => {
    try {
      // Check if we're on web - Google Sign-In package doesn't work on web
      if (typeof window !== 'undefined') {
        setState(prev => ({
          ...prev,
          error: 'Google Sign-In is not available on web. Please use the mobile app.',
          isConfigured: false
        }));
        return;
      }

      await GoogleSignin.configure({
        webClientId: '83272932146-YOUR_WEB_CLIENT_ID.apps.googleusercontent.com', // This needs to be configured in Firebase Console
        offlineAccess: true,
        hostedDomain: '',
        forceCodeForRefreshToken: true,
      });

      setState(prev => ({ ...prev, isConfigured: true }));
    } catch (error: any) {
      console.error('Google Sign-In configuration error:', error);
      setState(prev => ({
        ...prev,
        error: 'Failed to configure Google Sign-In. Please ensure you have Google Play Services.',
        isConfigured: false
      }));
    }
  };

  const signInWithGoogle = async () => {
    if (!state.isConfigured) {
      setState(prev => ({ ...prev, error: 'Google Sign-In not configured' }));
      return null;
    }

    setState(prev => ({ ...prev, isSigningIn: true, error: null }));

    try {
      // Check if device supports Google Play Services
      await GoogleSignin.hasPlayServices();
      
      // Get user info from Google
      const userInfo = await GoogleSignin.signIn();
      
      if (!userInfo.idToken) {
        throw new Error('No ID token received from Google');
      }

      // Create Firebase credential
      const googleCredential = GoogleAuthProvider.credential(userInfo.idToken);
      
      // Sign in to Firebase with Google credential
      const userCredential = await signInWithCredential(auth, googleCredential);
      
      setState(prev => ({ ...prev, isSigningIn: false }));
      return userCredential.user;
      
    } catch (error: any) {
      console.error('Google Sign-In error:', error);
      let errorMessage = 'Google Sign-In failed';
      
      if (error.code === 'auth/account-exists-with-different-credential') {
        errorMessage = 'An account already exists with this email using a different sign-in method';
      } else if (error.code === 'auth/invalid-credential') {
        errorMessage = 'Invalid Google credentials';
      } else if (error.code === 'auth/operation-not-allowed') {
        errorMessage = 'Google Sign-In is not enabled for this project';
      } else if (error.code === 'auth/user-disabled') {
        errorMessage = 'This account has been disabled';
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      setState(prev => ({ 
        ...prev, 
        isSigningIn: false, 
        error: errorMessage 
      }));
      return null;
    }
  };

  const signOut = async () => {
    try {
      await GoogleSignin.signOut();
      await auth.signOut();
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  return {
    ...state,
    signInWithGoogle,
    signOut,
  };
}

export default useGoogleAuth;
