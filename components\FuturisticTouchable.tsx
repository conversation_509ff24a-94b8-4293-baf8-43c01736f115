import { useAppTheme } from '@/app/ThemeContext';
import { Colors } from '@/constants/Colors';
import React, { useState } from 'react';
import { Animated, Platform, StyleProp, StyleSheet, TouchableOpacity, ViewStyle } from 'react-native';

interface FuturisticTouchableProps {
  children: React.ReactNode;
  onPress?: () => void;
  style?: StyleProp<ViewStyle>;
  activeOpacity?: number;
  disabled?: boolean;
  glowColor?: string;
  glowIntensity?: number;
  variant?: 'default' | 'neon' | 'pulse' | 'ripple' | 'hologram';
  animationDuration?: number;
}

export const FuturisticTouchable: React.FC<FuturisticTouchableProps> = ({
  children,
  onPress,
  style,
  activeOpacity = 0.8,
  disabled = false,
  glowColor,
  glowIntensity = 1.5,
  variant = 'default',
  animationDuration = 200,
}) => {
  const { colorScheme } = useAppTheme();
  const colors = Colors[colorScheme ?? 'light'];
  
  // Enhance glow intensity in dark mode
  const enhancedGlowIntensity = colorScheme === 'dark' ? glowIntensity * 1.8 : glowIntensity;

  const [scaleAnim] = useState(new Animated.Value(1));
  const [glowAnim] = useState(new Animated.Value(0));
  const [rippleAnim] = useState(new Animated.Value(0));
  const [pulseAnim] = useState(new Animated.Value(1));
  const [hologramAnim] = useState(new Animated.Value(0));
  const [borderAnim] = useState(new Animated.Value(0));
  const [isHovered, setIsHovered] = useState(false);

  // Enhanced glow color system
  const effectiveGlowColor = glowColor || colors.neon;

  const handlePressIn = () => {
    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(glowAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: false,
      })
    ]).start();
  };

  const handlePressOut = () => {
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 4,
        tension: 40,
        useNativeDriver: true,
      }),
      Animated.timing(glowAnim, {
        toValue: isHovered ? 0.5 : 0, // Keep subtle glow if hovered
        duration: 300,
        useNativeDriver: false,
      })
    ]).start();
  };

  // Handle hover for web
  const handleHoverIn = () => {
    setIsHovered(true);
    Animated.timing(glowAnim, {
      toValue: 0.5,
      duration: 200,
      useNativeDriver: false,
    }).start();
  };

  const handleHoverOut = () => {
    setIsHovered(false);
    Animated.timing(glowAnim, {
      toValue: 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
  };

  // Create shadow and border glow effect based on theme (non-native driver)
  const shadowColor = effectiveGlowColor;
  const glowStyle = {
    shadowColor,
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: glowAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [colorScheme === 'dark' ? 0.2 : 0, 0.7 * enhancedGlowIntensity],
    }) as unknown as number,
    shadowRadius: glowAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [colorScheme === 'dark' ? 5 : 0, 8 * enhancedGlowIntensity],
    }) as unknown as number,
    borderColor: effectiveGlowColor,
    borderWidth: colorScheme === 'dark' ? 1.5 : 1,
    borderRadius: 12,
  };

  // Create scale transform style (native driver)
  const scaleStyle = {
    transform: [{ scale: scaleAnim }]
  };

  // Web-specific hover props
  const hoverProps = Platform.OS === 'web'
    ? {
        onMouseEnter: handleHoverIn,
        onMouseLeave: handleHoverOut,
      }
    : {};

  return (
    <Animated.View style={[styles.container, glowStyle]}>
      <Animated.View style={scaleStyle}>
        <TouchableOpacity
          onPress={onPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          activeOpacity={activeOpacity}
          disabled={disabled}
          style={[styles.touchable, style]}
          {...hoverProps}
        >
          {children}
        </TouchableOpacity>
      </Animated.View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    overflow: 'visible',
  },
  touchable: {
    borderRadius: 12,
    overflow: 'hidden',
  },
});