import React from 'react';
import { StyleSheet, View, ViewStyle } from 'react-native';
import { useAppTheme } from '@/app/ThemeContext';
import { Colors } from '@/constants/Colors';

interface GlowContainerProps {
  children: React.ReactNode;
  intensity?: number;
  color?: string;
  style?: ViewStyle;
}

/**
 * A container component that adds a glow effect to its children in dark mode
 */
export const GlowContainer: React.FC<GlowContainerProps> = ({
  children,
  intensity = 1,
  color,
  style,
}) => {
  const { colorScheme, glowEnabled } = useAppTheme();
  const colors = Colors[colorScheme ?? 'light'];
  
  // Only apply glow in dark mode and if enabled
  const shouldGlow = colorScheme === 'dark' && glowEnabled;
  const glowColor = color || colors.neon;
  
  return (
    <View
      style={[
        styles.container,
        shouldGlow && {
          shadowColor: glowColor,
          shadowOpacity: 0.3 * intensity,
          shadowRadius: 15 * intensity,
          shadowOffset: { width: 0, height: 0 },
        },
        style,
      ]}
    >
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    overflow: 'visible',
  },
});