import React from 'react';
import { useTranslation } from 'react-i18next';
import { Alert, StyleSheet, Switch } from 'react-native';

import { useAppTheme } from '@/app/ThemeContext';
import ParallaxScrollView from '@/components/ParallaxScrollView';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { FuturisticCard } from '@/components/ui/FuturisticCard';
import { FuturisticGrid } from '@/components/ui/FuturisticGrid';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useCurrency } from '../../utils/currency';
import { getResponsiveFontSize, getResponsiveSpacing, isSmallScreen, responsiveDimensions } from '../../utils/responsive';

export default function LockScreen() {
  const { colorScheme } = useAppTheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { t } = useTranslation();
  const { formatCurrency } = useCurrency();
  const { user } = useAuth();
  const {
    blockedApps: userBlockedApps,
    activeApps,
    loading,
    error,
    toggleAppBlock: toggleAppBlockService,
    createBlockedApp
  } = useBlockedApps();

  // Calculate stats from real data
  const blockedCount = activeApps.length;
  const totalPenalty = activeApps.reduce((sum, app) => sum + app.penaltyAmount, 0);

  // Mock data for demo (replace with userBlockedApps when ready)
  const blockedApps: any[] = [
    {
      id: '1',
      name: 'Instagram',
      icon: 'camera.fill',
      penaltyAmount: 5,
      isBlocked: true,
    },
    {
      id: '2',
      name: 'TikTok',
      icon: 'play.circle.fill',
      penaltyAmount: 10,
      isBlocked: false,
    },
    {
      id: '3',
      name: 'Twitter',
      icon: 'message.circle.fill',
      penaltyAmount: 3,
      isBlocked: true,
    },
  ];

  const toggleAppBlock = async (appId: string) => {
    const app = blockedApps.find(a => a.id === appId);
    if (!app) return;

    try {
      Alert.alert(
        app.isBlocked ? t('lock.unblockApp') : t('lock.blockApp'),
        app.isBlocked
          ? t('lock.unblockAppWarning', { app: app.name, amount: app.penaltyAmount })
          : t('lock.blockAppConfirm', { app: app.name, amount: app.penaltyAmount }),
        [
          { text: t('common.cancel'), style: 'cancel' },
          {
            text: t('common.ok'),
            onPress: async () => {
              // For demo, just toggle the mock data
              app.isBlocked = !app.isBlocked;

              // TODO: When ready, use real Firebase service:
              // await toggleAppBlockService(appId, !app.isActive);
            }
          }
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to toggle app block');
    }
  };

  // Show loading state
  if (loading && !user) {
    return (
      <ThemedView style={styles.container}>
        <ThemedText>{t('common.loading')}</ThemedText>
      </ThemedView>
    );
  }

  // Show auth required state
  if (!user) {
    return (
      <ThemedView style={styles.container}>
        <ThemedText>Please sign in to manage blocked apps</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <FuturisticGrid size={40} opacity={0.1} />
      
      <ParallaxScrollView
        headerBackgroundColor={{
          light: Colors.light.background,
          dark: Colors.dark.background
        }}
        headerImage={
          <ThemedView style={styles.headerImageContainer}>
            <IconSymbol
              size={responsiveDimensions.header.iconSize}
              name="lock.fill"
              color={colors.tint}
            />
            <ThemedText type="title" style={[styles.headerTitle, { color: colors.tint }]}>
              {t('lock.title')}
            </ThemedText>
            <ThemedText style={[styles.headerSubtitle, { color: colors.textSecondary }]}>
              {t('subtitles.lock')}
            </ThemedText>
          </ThemedView>
        }>
        <ThemedView style={styles.content}>
          {/* Stats Grid */}
          <ThemedView style={styles.statsGrid}>
            <FuturisticCard variant="neon" style={styles.statCard} glowIntensity={1.2}>
              <IconSymbol size={24} name="lock.shield.fill" color={colors.tint} />
              <ThemedText style={[styles.statNumber, { color: colors.tint }]}>
                {blockedCount}
              </ThemedText>
              <ThemedText style={styles.statLabel}>
                {t('lock.blockedApps')}
              </ThemedText>
            </FuturisticCard>

            <FuturisticCard variant="glass" style={styles.statCard}>
              <ThemedText style={[styles.statNumber, { color: colors.secondary }]}>
                {formatCurrency(totalPenalty)}
              </ThemedText>
              <ThemedText style={styles.statLabel}>
                {t('lock.totalPenalty')}
              </ThemedText>
            </FuturisticCard>
          </ThemedView>

          {/* Apps List Section */}
          <ThemedView style={styles.section}>
            <ThemedText style={[styles.sectionTitle, { color: colors.tint }]}>
              {t('lock.manageApps')}
            </ThemedText>
            <ThemedText style={[styles.sectionDesc, { color: colors.textSecondary }]}>
              {t('lock.manageAppsDesc')}
            </ThemedText>

            <ThemedView style={styles.appsList}>
              {blockedApps.map((app) => (
                <FuturisticCard
                  key={app.id}
                  variant={app.isBlocked ? "glass" : "outlined"}
                  style={styles.appItem}
                  glowIntensity={app.isBlocked ? 1.5 : 0.3}
                  onPress={() => toggleAppBlock(app.id)}
                >
                  <ThemedView style={styles.appInfo}>
                    <IconSymbol
                      size={32}
                      name={app.icon as any}
                      color={app.isBlocked ? colors.tint : colors.icon}
                    />
                    <ThemedView style={styles.appDetails}>
                      <ThemedText style={styles.appName}>{app.name}</ThemedText>
                      <ThemedText style={[styles.penaltyText, { color: colors.textMuted }]}>
                        {t('lock.penaltyAmount', { amount: app.penaltyAmount })}
                      </ThemedText>
                    </ThemedView>
                  </ThemedView>

                  <ThemedView style={styles.appStatus}>
                    <ThemedText
                      style={[
                        styles.statusText,
                        { color: app.isBlocked ? colors.tint : colors.textMuted }
                      ]}
                    >
                      {app.isBlocked ? t('lock.blocked') : t('lock.unblocked')}
                    </ThemedText>
                    <Switch
                      value={app.isBlocked}
                      onValueChange={() => toggleAppBlock(app.id)}
                      trackColor={{ false: colors.border, true: colors.tint }}
                      thumbColor={app.isBlocked ? '#ffffff' : colors.icon}
                    />
                  </ThemedView>
                </FuturisticCard>
              ))}
            </ThemedView>
          </ThemedView>
        </ThemedView>
      </ParallaxScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  headerImageContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: getResponsiveSpacing('lg'),
    gap: getResponsiveSpacing('sm'),
  },
  headerTitle: {
    fontSize: getResponsiveFontSize('header'),
    fontWeight: '700',
    textAlign: 'center',
    letterSpacing: 1,
  },
  headerSubtitle: {
    fontSize: getResponsiveFontSize('sm'),
    textAlign: 'center',
    opacity: 0.8,
    letterSpacing: 0.5,
  },
  content: {
    gap: getResponsiveSpacing('xl'),
    paddingBottom: getResponsiveSpacing('xl'),
  },
  section: {
    gap: getResponsiveSpacing('md'),
  },
  sectionTitle: {
    fontSize: getResponsiveFontSize('lg'),
    fontWeight: '600',
    letterSpacing: 0.5,
    marginBottom: getResponsiveSpacing('xs'),
  },
  sectionDesc: {
    fontSize: getResponsiveFontSize('sm'),
    opacity: 0.8,
    marginBottom: getResponsiveSpacing('md'),
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: getResponsiveSpacing('sm'),
    justifyContent: 'space-between',
  },
  statCard: {
    alignItems: 'center',
    justifyContent: 'center',
    gap: getResponsiveSpacing('xs'),
    flex: isSmallScreen ? 1 : 0,
    minWidth: isSmallScreen ? '48%' : 120,
    minHeight: responsiveDimensions.cardHeight.md,
  },
  statNumber: {
    fontSize: getResponsiveFontSize('xl'),
    fontWeight: '700',
    textAlign: 'center',
    letterSpacing: 0.5,
  },
  statLabel: {
    fontSize: getResponsiveFontSize('xs'),
    textAlign: 'center',
    opacity: 0.8,
    fontWeight: '500',
  },
  appsList: {
    gap: getResponsiveSpacing('sm'),
  },
  appItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: getResponsiveSpacing('sm'),
  },
  appInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  appDetails: {
    marginLeft: getResponsiveSpacing('sm'),
    flex: 1,
  },
  appName: {
    fontSize: getResponsiveFontSize('md'),
    fontWeight: '600',
  },
  penaltyText: {
    fontSize: getResponsiveFontSize('xs'),
    marginTop: getResponsiveSpacing('xs'),
  },
  appStatus: {
    alignItems: 'flex-end',
    gap: getResponsiveSpacing('xs'),
  },
  statusText: {
    fontSize: getResponsiveFontSize('xs'),
    fontWeight: '600',
  },
});
