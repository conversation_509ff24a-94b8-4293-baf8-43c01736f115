import { LanguageCode } from '@/constants/Languages';
import { ThemeName } from '@/app/ThemeContext';

// Base types
export interface Timestamp {
  seconds: number;
  nanoseconds: number;
}

// User related types
export interface User {
  id: string;
  email: string;
  displayName: string;
  guardians: string[]; // Guardian phone numbers or emails
  settings: UserSettings;
  stats: UserStats;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserSettings {
  theme: ThemeName;
  language: LanguageCode;
  graceUnlocksUsed: number;
  maxGraceUnlocksPerMonth: number; // Default: 2
  maxMonthlyPenalty: number; // Default: $100
  glowEnabled: boolean;
  notificationsEnabled: boolean;
  guardianNotificationsEnabled: boolean;
}

export interface UserStats {
  focusStreak: number; // Days
  totalFocusHours: number;
  totalSaved: number; // Money saved by not paying penalties
  totalPenaltiesPaid: number;
  efficiency: number; // Percentage
  longestStreak: number;
  appsBlocked: number;
  successfulBlocks: number;
}

// Blocked Apps types
export interface BlockedApp {
  id: string;
  userId: string;
  appName: string;
  packageName: string; // Android package name
  bundleId: string; // iOS bundle identifier
  iconUrl?: string;
  penaltyAmount: number;
  isActive: boolean;
  blockStartTime: Date;
  blockEndTime?: Date;
  blockDuration: number; // minutes
  unlockAttempts: number;
  successfulUnlocks: number;
  totalTimeBlocked: number; // minutes
  createdAt: Date;
  updatedAt: Date;
}

// Penalty types
export type PenaltyStatus = 'pending' | 'paid' | 'forgiven' | 'overdue';
export type PaymentMethod = 'instant' | '30day' | 'charity' | 'grace';

export interface Penalty {
  id: string;
  userId: string;
  blockedAppId: string;
  appName: string;
  amount: number;
  timestamp: Date;
  status: PenaltyStatus;
  paymentMethod: PaymentMethod;
  paymentDetails?: PaymentDetails;
  dueDate?: Date; // For 30-day payments
  paidAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface PaymentDetails {
  transactionId?: string;
  charityName?: string;
  charityId?: string;
  guardianApproval?: boolean;
  guardianId?: string;
  stripePaymentIntentId?: string;
  paypalOrderId?: string;
}

// Guardian Code types
export interface GuardianCode {
  id: string;
  userId: string;
  code: string;
  guardianContact: string; // Phone or email
  appName: string;
  blockedAppId: string;
  expiresAt: Date;
  used: boolean;
  usedAt?: Date;
  createdAt: Date;
}

// Focus Session types
export interface FocusSession {
  id: string;
  userId: string;
  startTime: Date;
  endTime?: Date;
  duration: number; // minutes
  blockedApps: string[]; // App names
  successful: boolean;
  penaltiesIncurred: number;
  moneyAtRisk: number;
  moneySaved: number;
  createdAt: Date;
}

// Achievement types
export type AchievementType = 
  | 'focus_warrior' 
  | 'money_saver' 
  | 'streak_master' 
  | 'guardian_angel' 
  | 'penalty_free';

export interface Achievement {
  id: string;
  userId: string;
  type: AchievementType;
  title: string;
  description: string;
  iconName: string;
  unlockedAt: Date;
  progress: number; // 0-100
  target: number;
  createdAt: Date;
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Firestore collection names
export const COLLECTIONS = {
  USERS: 'users',
  BLOCKED_APPS: 'blockedApps',
  PENALTIES: 'penalties',
  GUARDIAN_CODES: 'guardianCodes',
  FOCUS_SESSIONS: 'focusSessions',
  ACHIEVEMENTS: 'achievements',
} as const;

// Error types
export class FeeFenceError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'FeeFenceError';
  }
}

export type ErrorCode = 
  | 'AUTH_REQUIRED'
  | 'PERMISSION_DENIED'
  | 'NOT_FOUND'
  | 'INVALID_INPUT'
  | 'PAYMENT_FAILED'
  | 'GUARDIAN_CODE_EXPIRED'
  | 'GRACE_LIMIT_EXCEEDED'
  | 'MONTHLY_PENALTY_LIMIT_EXCEEDED';
