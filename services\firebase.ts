import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyAsAOt7GGkWKKJLZp3MVdozcUB_ghoLqOc",
  authDomain: "feefence.firebaseapp.com",
  projectId: "feefence",
  storageBucket: "feefence.firebasestorage.app",
  messagingSenderId: "83272932146",
  appId: "1:83272932146:web:8919ce6c404d8d1497d52b",
  measurementId: "G-T0CJFQW6JB"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const db = getFirestore(app);

// Initialize Auth with error handling
let auth: any;
try {
  auth = getAuth(app);
} catch (error) {
  console.warn('Firebase Auth initialization failed:', error);
  auth = null;
}
export { auth };

export default app;
