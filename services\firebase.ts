import AsyncStorage from '@react-native-async-storage/async-storage';
import { initializeApp } from 'firebase/app';
import { getReactNativePersistence, initializeAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyAsAOt7GGkWKKJLZp3MVdozcUB_ghoLqOc",
  authDomain: "feefence.firebaseapp.com",
  projectId: "feefence",
  storageBucket: "feefence.firebasestorage.app",
  messagingSenderId: "83272932146",
  appId: "1:83272932146:web:8919ce6c404d8d1497d52b",
  measurementId: "G-T0CJFQW6JB"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const db = getFirestore(app);

// Initialize Auth with React Native persistence
let auth: any;
try {
  // Try React Native specific auth first
  auth = initializeAuth(app, {
    persistence: getReactNativePersistence(AsyncStorage)
  });
} catch (error: any) {
  console.warn('React Native Auth initialization failed, trying fallback:', error);
  try {
    // Fallback to regular getAuth for web/development
    const { getAuth } = require('firebase/auth');
    auth = getAuth(app);
  } catch (fallbackError) {
    console.warn('All Firebase Auth initialization failed:', fallbackError);
    auth = null;
  }
}
export { auth };

export default app;
