import Constants from 'expo-constants';
import { getAnalytics } from 'firebase/analytics';
import { initializeApp } from 'firebase/app';
import { connectAuthEmulator, getAuth } from 'firebase/auth';
import { connectFirestoreEmulator, getFirestore } from 'firebase/firestore';
import { connectFunctionsEmulator, getFunctions } from 'firebase/functions';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyAsAOt7GGkWKKJLZp3MVdozcUB_ghoLqOc",
  authDomain: "feefence.firebaseapp.com",
  projectId: "feefence",
  storageBucket: "feefence.firebasestorage.app",
  messagingSenderId: "83272932146",
  appId: "1:83272932146:web:8919ce6c404d8d1497d52b",
  measurementId: "G-T0CJFQW6JB"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const db = getFirestore(app);
export const auth = getAuth(app);
export const functions = getFunctions(app);

// Initialize Analytics (only on web)
let analytics: any = null;
if (typeof window !== 'undefined') {
  try {
    analytics = getAnalytics(app);
  } catch (error) {
    console.log('Analytics not available:', error);
  }
}
export { analytics };

// Connect to emulators in development
if (__DEV__ && Constants.expoConfig?.extra?.useFirebaseEmulator) {
  // Connect to Firestore emulator
  try {
    connectFirestoreEmulator(db, 'localhost', 8080);
  } catch (error) {
    console.log('Firestore emulator already connected');
  }

  // Connect to Auth emulator
  try {
    connectAuthEmulator(auth, 'http://localhost:9099');
  } catch (error) {
    console.log('Auth emulator already connected');
  }

  // Connect to Functions emulator
  try {
    connectFunctionsEmulator(functions, 'localhost', 5001);
  } catch (error) {
    console.log('Functions emulator already connected');
  }
}

export default app;
