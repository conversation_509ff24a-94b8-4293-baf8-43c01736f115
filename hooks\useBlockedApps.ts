import { useEffect, useState, useCallback } from 'react';
import { BlockedApp, FeeFenceError } from '@/types';
import BlockedAppService from '@/services/blockedAppService';
import { useAuth } from './useAuth';

interface UseBlockedAppsState {
  blockedApps: BlockedApp[];
  activeApps: BlockedApp[];
  loading: boolean;
  error: string | null;
}

export function useBlockedApps() {
  const { user } = useAuth();
  const [state, setState] = useState<UseBlockedAppsState>({
    blockedApps: [],
    activeApps: [],
    loading: true,
    error: null,
  });

  // Subscribe to blocked apps
  useEffect(() => {
    if (!user) {
      setState({
        blockedApps: [],
        activeApps: [],
        loading: false,
        error: null,
      });
      return;
    }

    setState(prev => ({ ...prev, loading: true, error: null }));

    // Subscribe to all blocked apps
    const unsubscribeAll = BlockedAppService.subscribeToUserBlockedApps(
      user.uid,
      (blockedApps) => {
        setState(prev => ({ 
          ...prev, 
          blockedApps,
          loading: false,
          error: null,
        }));
      },
      (error) => {
        setState(prev => ({ 
          ...prev, 
          loading: false,
          error: error.message,
        }));
      }
    );

    // Subscribe to active blocked apps
    const unsubscribeActive = BlockedAppService.subscribeToActiveBlockedApps(
      user.uid,
      (activeApps) => {
        setState(prev => ({ 
          ...prev, 
          activeApps,
        }));
      },
      (error) => {
        console.error('Error subscribing to active blocked apps:', error);
      }
    );

    return () => {
      unsubscribeAll();
      unsubscribeActive();
    };
  }, [user]);

  // Create a new blocked app
  const createBlockedApp = useCallback(async (
    appName: string,
    packageName: string,
    bundleId: string,
    penaltyAmount: number,
    blockDuration: number,
    iconUrl?: string
  ): Promise<string> => {
    if (!user) {
      throw new FeeFenceError('User not authenticated', 'AUTH_REQUIRED');
    }

    try {
      const id = await BlockedAppService.createBlockedApp(
        user.uid,
        appName,
        packageName,
        bundleId,
        penaltyAmount,
        blockDuration,
        iconUrl
      );
      return id;
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to create blocked app',
      }));
      throw error;
    }
  }, [user]);

  // Toggle app block status
  const toggleAppBlock = useCallback(async (
    appId: string,
    isActive: boolean
  ): Promise<void> => {
    try {
      await BlockedAppService.toggleBlockedApp(appId, isActive);
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to toggle app block',
      }));
      throw error;
    }
  }, []);

  // Update penalty amount
  const updatePenaltyAmount = useCallback(async (
    appId: string,
    newAmount: number
  ): Promise<void> => {
    try {
      await BlockedAppService.updatePenaltyAmount(appId, newAmount);
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to update penalty amount',
      }));
      throw error;
    }
  }, []);

  // Update block duration
  const updateBlockDuration = useCallback(async (
    appId: string,
    newDuration: number
  ): Promise<void> => {
    try {
      await BlockedAppService.updateBlockDuration(appId, newDuration);
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to update block duration',
      }));
      throw error;
    }
  }, []);

  // Record unlock attempt
  const recordUnlockAttempt = useCallback(async (appId: string): Promise<void> => {
    try {
      await BlockedAppService.recordUnlockAttempt(appId);
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to record unlock attempt',
      }));
      throw error;
    }
  }, []);

  // Record successful unlock
  const recordSuccessfulUnlock = useCallback(async (appId: string): Promise<void> => {
    try {
      await BlockedAppService.recordSuccessfulUnlock(appId);
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to record successful unlock',
      }));
      throw error;
    }
  }, []);

  // Delete blocked app
  const deleteBlockedApp = useCallback(async (appId: string): Promise<void> => {
    try {
      await BlockedAppService.deleteBlockedApp(appId);
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to delete blocked app',
      }));
      throw error;
    }
  }, []);

  // Check if app is blocked
  const isAppBlocked = useCallback(async (
    packageName: string,
    bundleId: string
  ): Promise<boolean> => {
    if (!user) return false;

    try {
      return await BlockedAppService.isAppBlocked(user.uid, packageName, bundleId);
    } catch (error) {
      console.error('Error checking if app is blocked:', error);
      return false;
    }
  }, [user]);

  // Get blocked app by identifier
  const getBlockedAppByIdentifier = useCallback(async (
    packageName?: string,
    bundleId?: string
  ): Promise<BlockedApp | null> => {
    if (!user) return null;

    try {
      return await BlockedAppService.getBlockedAppByIdentifier(
        user.uid,
        packageName,
        bundleId
      );
    } catch (error) {
      console.error('Error getting blocked app by identifier:', error);
      return null;
    }
  }, [user]);

  // Get summary statistics
  const getSummary = useCallback(async () => {
    if (!user) return null;

    try {
      return await BlockedAppService.getBlockedAppsSummary(user.uid);
    } catch (error) {
      console.error('Error getting blocked apps summary:', error);
      return null;
    }
  }, [user]);

  // Clear error
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    blockedApps: state.blockedApps,
    activeApps: state.activeApps,
    loading: state.loading,
    error: state.error,
    createBlockedApp,
    toggleAppBlock,
    updatePenaltyAmount,
    updateBlockDuration,
    recordUnlockAttempt,
    recordSuccessfulUnlock,
    deleteBlockedApp,
    isAppBlocked,
    getBlockedAppByIdentifier,
    getSummary,
    clearError,
  };
}

export default useBlockedApps;
