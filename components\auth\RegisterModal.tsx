import React, { useState } from 'react';
import {
  Modal,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { createUserWithEmailAndPassword, updateProfile } from 'firebase/auth';
import { auth } from '@/services/firebase';
import { useAppTheme } from '@/app/ThemeContext';
import { Colors } from '@/constants/Colors';
import { FuturisticCard } from '@/components/ui/FuturisticCard';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { getResponsiveFontSize, getResponsiveSpacing } from '@/utils/responsive';

interface RegisterModalProps {
  visible: boolean;
  onClose: () => void;
  onSwitchToLogin: () => void;
}

export function RegisterModal({ visible, onClose, onSwitchToLogin }: RegisterModalProps) {
  const { t } = useTranslation();
  const { colorScheme } = useAppTheme();
  const colors = Colors[colorScheme ?? 'light'];
  
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const validateForm = () => {
    if (!name.trim()) {
      Alert.alert(t('auth.error'), t('auth.nameRequired'));
      return false;
    }
    
    if (!email.trim()) {
      Alert.alert(t('auth.error'), t('auth.emailRequired'));
      return false;
    }
    
    if (password.length < 6) {
      Alert.alert(t('auth.error'), t('auth.passwordTooShort'));
      return false;
    }
    
    if (password !== confirmPassword) {
      Alert.alert(t('auth.error'), t('auth.passwordsDoNotMatch'));
      return false;
    }
    
    return true;
  };

  const handleRegister = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      // Create user account
      const userCredential = await createUserWithEmailAndPassword(
        auth, 
        email.trim(), 
        password
      );
      
      // Update user profile with display name
      await updateProfile(userCredential.user, {
        displayName: name.trim(),
      });

      onClose();
      clearForm();
      Alert.alert(
        t('auth.success'), 
        t('auth.accountCreated'),
        [{ text: t('common.ok') }]
      );
    } catch (error: any) {
      console.error('Registration error:', error);
      let errorMessage = t('auth.registrationFailed');
      
      switch (error.code) {
        case 'auth/email-already-in-use':
          errorMessage = t('auth.emailAlreadyInUse');
          break;
        case 'auth/invalid-email':
          errorMessage = t('auth.invalidEmail');
          break;
        case 'auth/weak-password':
          errorMessage = t('auth.weakPassword');
          break;
        case 'auth/network-request-failed':
          errorMessage = t('auth.networkError');
          break;
      }
      
      Alert.alert(t('auth.error'), errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const clearForm = () => {
    setName('');
    setEmail('');
    setPassword('');
    setConfirmPassword('');
  };

  const handleClose = () => {
    clearForm();
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={handleClose}
    >
      <KeyboardAvoidingView 
        style={styles.overlay}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <TouchableOpacity 
          style={styles.backdrop} 
          activeOpacity={1} 
          onPress={handleClose}
        >
          <FuturisticCard 
            variant="glass" 
            style={[styles.modal, { backgroundColor: colors.background }]}
            glowIntensity={2}
            onPress={() => {}} // Prevent modal from closing when tapping inside
          >
            {/* Header */}
            <View style={styles.header}>
              <IconSymbol 
                size={32} 
                name="person.badge.plus" 
                color={colors.tint} 
              />
              <Text style={[styles.title, { color: colors.text }]}>
                {t('auth.signUp')}
              </Text>
              <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
                <IconSymbol size={24} name="xmark" color={colors.textMuted} />
              </TouchableOpacity>
            </View>

            {/* Form */}
            <View style={styles.form}>
              <View style={styles.inputContainer}>
                <Text style={[styles.label, { color: colors.textSecondary }]}>
                  {t('auth.fullName')}
                </Text>
                <TextInput
                  style={[styles.input, { 
                    borderColor: colors.border, 
                    color: colors.text,
                    backgroundColor: colors.background + '80'
                  }]}
                  value={name}
                  onChangeText={setName}
                  placeholder={t('auth.fullNamePlaceholder')}
                  placeholderTextColor={colors.textMuted}
                  autoCapitalize="words"
                  autoCorrect={false}
                />
              </View>

              <View style={styles.inputContainer}>
                <Text style={[styles.label, { color: colors.textSecondary }]}>
                  {t('auth.email')}
                </Text>
                <TextInput
                  style={[styles.input, { 
                    borderColor: colors.border, 
                    color: colors.text,
                    backgroundColor: colors.background + '80'
                  }]}
                  value={email}
                  onChangeText={setEmail}
                  placeholder={t('auth.emailPlaceholder')}
                  placeholderTextColor={colors.textMuted}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                />
              </View>

              <View style={styles.inputContainer}>
                <Text style={[styles.label, { color: colors.textSecondary }]}>
                  {t('auth.password')}
                </Text>
                <View style={styles.passwordContainer}>
                  <TextInput
                    style={[styles.passwordInput, { 
                      borderColor: colors.border, 
                      color: colors.text,
                      backgroundColor: colors.background + '80'
                    }]}
                    value={password}
                    onChangeText={setPassword}
                    placeholder={t('auth.passwordPlaceholder')}
                    placeholderTextColor={colors.textMuted}
                    secureTextEntry={!showPassword}
                    autoCapitalize="none"
                    autoCorrect={false}
                  />
                  <TouchableOpacity 
                    onPress={() => setShowPassword(!showPassword)}
                    style={styles.eyeButton}
                  >
                    <IconSymbol 
                      size={20} 
                      name={showPassword ? "eye.slash" : "eye"} 
                      color={colors.textMuted} 
                    />
                  </TouchableOpacity>
                </View>
              </View>

              <View style={styles.inputContainer}>
                <Text style={[styles.label, { color: colors.textSecondary }]}>
                  {t('auth.confirmPassword')}
                </Text>
                <View style={styles.passwordContainer}>
                  <TextInput
                    style={[styles.passwordInput, { 
                      borderColor: colors.border, 
                      color: colors.text,
                      backgroundColor: colors.background + '80'
                    }]}
                    value={confirmPassword}
                    onChangeText={setConfirmPassword}
                    placeholder={t('auth.confirmPasswordPlaceholder')}
                    placeholderTextColor={colors.textMuted}
                    secureTextEntry={!showConfirmPassword}
                    autoCapitalize="none"
                    autoCorrect={false}
                  />
                  <TouchableOpacity 
                    onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                    style={styles.eyeButton}
                  >
                    <IconSymbol 
                      size={20} 
                      name={showConfirmPassword ? "eye.slash" : "eye"} 
                      color={colors.textMuted} 
                    />
                  </TouchableOpacity>
                </View>
              </View>

              {/* Register Button */}
              <TouchableOpacity
                style={[styles.registerButton, { backgroundColor: colors.tint }]}
                onPress={handleRegister}
                disabled={loading}
              >
                {loading ? (
                  <ActivityIndicator color="#ffffff" />
                ) : (
                  <Text style={styles.registerButtonText}>
                    {t('auth.createAccount')}
                  </Text>
                )}
              </TouchableOpacity>

              {/* Switch to Login */}
              <View style={styles.switchContainer}>
                <Text style={[styles.switchText, { color: colors.textMuted }]}>
                  {t('auth.haveAccount')}
                </Text>
                <TouchableOpacity onPress={onSwitchToLogin}>
                  <Text style={[styles.switchLink, { color: colors.tint }]}>
                    {t('auth.signIn')}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </FuturisticCard>
        </TouchableOpacity>
      </KeyboardAvoidingView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  backdrop: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: getResponsiveSpacing('lg'),
  },
  modal: {
    width: '100%',
    maxWidth: 400,
    padding: getResponsiveSpacing('lg'),
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: getResponsiveSpacing('lg'),
  },
  title: {
    fontSize: getResponsiveFontSize('lg'),
    fontWeight: '700',
    flex: 1,
    textAlign: 'center',
    marginLeft: -24, // Compensate for close button
  },
  closeButton: {
    padding: getResponsiveSpacing('xs'),
  },
  form: {
    gap: getResponsiveSpacing('md'),
  },
  inputContainer: {
    gap: getResponsiveSpacing('xs'),
  },
  label: {
    fontSize: getResponsiveFontSize('sm'),
    fontWeight: '600',
  },
  input: {
    borderWidth: 1,
    borderRadius: 12,
    padding: getResponsiveSpacing('sm'),
    fontSize: getResponsiveFontSize('md'),
  },
  passwordContainer: {
    position: 'relative',
  },
  passwordInput: {
    borderWidth: 1,
    borderRadius: 12,
    padding: getResponsiveSpacing('sm'),
    paddingRight: 50,
    fontSize: getResponsiveFontSize('md'),
  },
  eyeButton: {
    position: 'absolute',
    right: getResponsiveSpacing('sm'),
    top: '50%',
    transform: [{ translateY: -10 }],
  },
  registerButton: {
    borderRadius: 12,
    padding: getResponsiveSpacing('md'),
    alignItems: 'center',
    marginTop: getResponsiveSpacing('sm'),
  },
  registerButtonText: {
    color: '#ffffff',
    fontSize: getResponsiveFontSize('md'),
    fontWeight: '600',
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: getResponsiveSpacing('xs'),
    marginTop: getResponsiveSpacing('sm'),
  },
  switchText: {
    fontSize: getResponsiveFontSize('sm'),
  },
  switchLink: {
    fontSize: getResponsiveFontSize('sm'),
    fontWeight: '600',
  },
});

export default RegisterModal;
