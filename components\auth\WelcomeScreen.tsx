import { useAppTheme } from '@/app/ThemeContext';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { getResponsiveFontSize, getResponsiveSpacing } from '@/utils/responsive';
import { LinearGradient } from 'expo-linear-gradient';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
    Dimensions,
    SafeAreaView,
    StatusBar,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import LoginModal from './LoginModal';
import RegisterModal from './RegisterModal';

const { width, height } = Dimensions.get('window');

export function WelcomeScreen() {
  const { t } = useTranslation();
  const { colorScheme } = useAppTheme();
  const colors = Colors[colorScheme ?? 'light'];

  // Import here to avoid auto-formatting issues
  const { useLanguage } = require('@/app/LanguageContext');
  const { FuturisticCard } = require('@/components/ui/FuturisticCard');
  const { LANGUAGES } = require('@/constants/Languages');

  const { language, setLanguage } = useLanguage();
  const [loginModalVisible, setLoginModalVisible] = useState(false);
  const [registerModalVisible, setRegisterModalVisible] = useState(false);
  const [languageDropdownVisible, setLanguageDropdownVisible] = useState(false);

  const handleSwitchToRegister = () => {
    setLoginModalVisible(false);
    setTimeout(() => setRegisterModalVisible(true), 300);
  };

  const handleSwitchToLogin = () => {
    setRegisterModalVisible(false);
    setTimeout(() => setLoginModalVisible(true), 300);
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar 
        barStyle={colorScheme === 'dark' ? 'light-content' : 'dark-content'} 
        backgroundColor={colors.background}
      />
      
      {/* Background Gradient */}
      <LinearGradient
        colors={[
          colors.background,
          colors.tint + '20',
          colors.background,
        ]}
        style={styles.backgroundGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />

      <View style={styles.content}>
        {/* Language Selector */}
        <View style={styles.languageSection}>
          <TouchableOpacity
            style={[styles.languageButton, { borderColor: colors.border }]}
            onPress={() => setLanguageDropdownVisible(!languageDropdownVisible)}
          >
            <Text style={styles.languageFlag}>
              {LANGUAGES.find(lang => lang.code === language)?.flag || '🌐'}
            </Text>
            <Text style={[styles.languageText, { color: colors.text }]}>
              {LANGUAGES.find(lang => lang.code === language)?.nativeName || 'Language'}
            </Text>
            <IconSymbol
              size={16}
              name={languageDropdownVisible ? "chevron.up" : "chevron.down"}
              color={colors.textMuted}
            />
          </TouchableOpacity>

          {languageDropdownVisible && (
            <FuturisticCard variant="glass" style={[styles.languageDropdown, { backgroundColor: colors.background }]}>
              {LANGUAGES.map((lang) => (
                <TouchableOpacity
                  key={lang.code}
                  style={[
                    styles.languageOption,
                    language === lang.code && { backgroundColor: colors.tint + '20' }
                  ]}
                  onPress={() => {
                    setLanguage(lang.code);
                    setLanguageDropdownVisible(false);
                  }}
                >
                  <Text style={styles.languageFlag}>{lang.flag}</Text>
                  <Text style={[styles.languageOptionText, { color: colors.text }]}>
                    {lang.nativeName}
                  </Text>
                  {language === lang.code && (
                    <IconSymbol size={16} name="checkmark" color={colors.tint} />
                  )}
                </TouchableOpacity>
              ))}
            </FuturisticCard>
          )}
        </View>

        {/* Logo Section */}
        <View style={styles.logoSection}>
          <IconSymbol
            size={120}
            name="lock.shield"
            color={colors.tint}
          />

          <Text style={[styles.appName, { color: colors.text }]}>
            FeeFence
          </Text>

          <Text style={[styles.tagline, { color: colors.textSecondary }]}>
            {t('welcome.tagline')}
          </Text>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionSection}>
          <TouchableOpacity
            style={[styles.primaryButton, { backgroundColor: colors.tint }]}
            onPress={() => setRegisterModalVisible(true)}
          >
            <Text style={styles.primaryButtonText}>
              {t('auth.getStarted')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.secondaryButton, { borderColor: colors.border }]}
            onPress={() => setLoginModalVisible(true)}
          >
            <Text style={[styles.secondaryButtonText, { color: colors.text }]}>
              {t('auth.signIn')}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Modals */}
      <LoginModal
        visible={loginModalVisible}
        onClose={() => setLoginModalVisible(false)}
        onSwitchToRegister={handleSwitchToRegister}
      />

      <RegisterModal
        visible={registerModalVisible}
        onClose={() => setRegisterModalVisible(false)}
        onSwitchToLogin={handleSwitchToLogin}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: getResponsiveSpacing('lg'),
  },
  languageSection: {
    position: 'absolute',
    top: getResponsiveSpacing('lg'),
    right: getResponsiveSpacing('lg'),
    zIndex: 1000,
  },
  languageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing('sm'),
    paddingVertical: getResponsiveSpacing('xs'),
    borderRadius: 20,
    borderWidth: 1,
    gap: getResponsiveSpacing('xs'),
    minWidth: 120,
  },
  languageFlag: {
    fontSize: getResponsiveFontSize('md'),
  },
  languageText: {
    fontSize: getResponsiveFontSize('sm'),
    fontWeight: '500',
    flex: 1,
  },
  languageDropdown: {
    position: 'absolute',
    top: '100%',
    right: 0,
    marginTop: getResponsiveSpacing('xs'),
    minWidth: 200,
    maxHeight: 300,
    borderRadius: 12,
    padding: getResponsiveSpacing('xs'),
  },
  languageOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: getResponsiveSpacing('sm'),
    paddingVertical: getResponsiveSpacing('sm'),
    borderRadius: 8,
    gap: getResponsiveSpacing('sm'),
  },
  languageOptionText: {
    fontSize: getResponsiveFontSize('sm'),
    fontWeight: '500',
    flex: 1,
  },
  logoSection: {
    alignItems: 'center',
    marginBottom: getResponsiveSpacing('xxl'),
  },
  appName: {
    fontSize: getResponsiveFontSize('xxl'),
    fontWeight: '800',
    marginTop: getResponsiveSpacing('lg'),
    marginBottom: getResponsiveSpacing('sm'),
    letterSpacing: 1,
  },
  tagline: {
    fontSize: getResponsiveFontSize('md'),
    textAlign: 'center',
    lineHeight: 24,
    maxWidth: width * 0.8,
  },
  actionSection: {
    width: '100%',
    maxWidth: 300,
    gap: getResponsiveSpacing('md'),
  },
  primaryButton: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: getResponsiveSpacing('lg'),
    borderRadius: 16,
  },
  primaryButtonText: {
    color: '#ffffff',
    fontSize: getResponsiveFontSize('lg'),
    fontWeight: '600',
  },
  secondaryButton: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: getResponsiveSpacing('lg'),
    borderRadius: 16,
    borderWidth: 2,
  },
  secondaryButtonText: {
    fontSize: getResponsiveFontSize('lg'),
    fontWeight: '600',
  },
});

export default WelcomeScreen;
