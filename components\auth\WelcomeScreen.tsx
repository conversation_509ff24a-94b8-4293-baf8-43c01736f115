import { useAppTheme } from '@/app/ThemeContext';
import { FuturisticCard } from '@/components/ui/FuturisticCard';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { getResponsiveFontSize, getResponsiveSpacing } from '@/utils/responsive';
import { LinearGradient } from 'expo-linear-gradient';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
    Dimensions,
    SafeAreaView,
    ScrollView,
    StatusBar,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import LoginModal from './LoginModal';
import RegisterModal from './RegisterModal';

const { width, height } = Dimensions.get('window');

export function WelcomeScreen() {
  const { t } = useTranslation();
  const { colorScheme } = useAppTheme();
  const colors = Colors[colorScheme ?? 'light'];
  
  const [loginModalVisible, setLoginModalVisible] = useState(false);
  const [registerModalVisible, setRegisterModalVisible] = useState(false);

  const handleSwitchToRegister = () => {
    setLoginModalVisible(false);
    setTimeout(() => setRegisterModalVisible(true), 300);
  };

  const handleSwitchToLogin = () => {
    setRegisterModalVisible(false);
    setTimeout(() => setLoginModalVisible(true), 300);
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar 
        barStyle={colorScheme === 'dark' ? 'light-content' : 'dark-content'} 
        backgroundColor={colors.background}
      />
      
      {/* Background Gradient */}
      <LinearGradient
        colors={[
          colors.background,
          colors.tint + '20',
          colors.background,
        ]}
        style={styles.backgroundGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Logo Section */}
        <View style={styles.logoSection}>
          <FuturisticCard
            variant="glass"
            style={styles.logoCard}
            glowIntensity={3}
          >
            <IconSymbol
              size={80}
              name="lock.shield"
              color={colors.tint}
            />
          </FuturisticCard>

          <Text style={[styles.appName, { color: colors.text }]}>
            FeeFence
          </Text>

          <Text style={[styles.tagline, { color: colors.textSecondary }]}>
            {t('welcome.tagline')}
          </Text>
        </View>

        {/* Features Section */}
        <View style={styles.featuresSection}>
          <FuturisticCard variant="glass" style={styles.featureCard}>
            <View style={styles.feature}>
              <IconSymbol size={24} name="shield.checkered" color={colors.tint} />
              <Text style={[styles.featureText, { color: colors.text }]}>
                {t('welcome.feature1')}
              </Text>
            </View>
          </FuturisticCard>

          <FuturisticCard variant="glass" style={styles.featureCard}>
            <View style={styles.feature}>
              <IconSymbol size={24} name="chart.line.uptrend.xyaxis" color={colors.tint} />
              <Text style={[styles.featureText, { color: colors.text }]}>
                {t('welcome.feature2')}
              </Text>
            </View>
          </FuturisticCard>

          <FuturisticCard variant="glass" style={styles.featureCard}>
            <View style={styles.feature}>
              <IconSymbol size={24} name="bell.badge" color={colors.tint} />
              <Text style={[styles.featureText, { color: colors.text }]}>
                {t('welcome.feature3')}
              </Text>
            </View>
          </FuturisticCard>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionSection}>
          <TouchableOpacity
            style={[styles.primaryButton, { backgroundColor: colors.tint }]}
            onPress={() => setRegisterModalVisible(true)}
          >
            <Text style={styles.primaryButtonText}>
              {t('auth.getStarted')}
            </Text>
            <IconSymbol size={20} name="arrow.right" color="#ffffff" />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.secondaryButton, { borderColor: colors.border }]}
            onPress={() => setLoginModalVisible(true)}
          >
            <Text style={[styles.secondaryButtonText, { color: colors.text }]}>
              {t('auth.signIn')}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={[styles.footerText, { color: colors.textMuted }]}>
            {t('welcome.footer')}
          </Text>
        </View>
      </ScrollView>

      {/* Modals */}
      <LoginModal
        visible={loginModalVisible}
        onClose={() => setLoginModalVisible(false)}
        onSwitchToRegister={handleSwitchToRegister}
      />

      <RegisterModal
        visible={registerModalVisible}
        onClose={() => setRegisterModalVisible(false)}
        onSwitchToLogin={handleSwitchToLogin}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: getResponsiveSpacing('lg'),
    paddingBottom: getResponsiveSpacing('xl'),
  },
  logoSection: {
    alignItems: 'center',
    marginTop: getResponsiveSpacing('xl'),
    marginBottom: getResponsiveSpacing('xl'),
  },
  logoCard: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: getResponsiveSpacing('lg'),
  },
  appName: {
    fontSize: getResponsiveFontSize('xxl'),
    fontWeight: '800',
    marginBottom: getResponsiveSpacing('sm'),
    letterSpacing: 1,
  },
  tagline: {
    fontSize: getResponsiveFontSize('md'),
    textAlign: 'center',
    lineHeight: 24,
    maxWidth: width * 0.8,
  },
  featuresSection: {
    gap: getResponsiveSpacing('md'),
    marginBottom: getResponsiveSpacing('xl'),
  },
  featureCard: {
    padding: getResponsiveSpacing('md'),
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: getResponsiveSpacing('md'),
  },
  featureText: {
    fontSize: getResponsiveFontSize('md'),
    fontWeight: '500',
    flex: 1,
  },
  actionSection: {
    gap: getResponsiveSpacing('md'),
    marginBottom: getResponsiveSpacing('lg'),
  },
  primaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: getResponsiveSpacing('lg'),
    borderRadius: 16,
    gap: getResponsiveSpacing('sm'),
  },
  primaryButtonText: {
    color: '#ffffff',
    fontSize: getResponsiveFontSize('lg'),
    fontWeight: '600',
  },
  secondaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: getResponsiveSpacing('lg'),
    borderRadius: 16,
    borderWidth: 2,
  },
  secondaryButtonText: {
    fontSize: getResponsiveFontSize('lg'),
    fontWeight: '600',
  },
  footer: {
    alignItems: 'center',
    marginTop: getResponsiveSpacing('lg'),
  },
  footerText: {
    fontSize: getResponsiveFontSize('sm'),
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default WelcomeScreen;
