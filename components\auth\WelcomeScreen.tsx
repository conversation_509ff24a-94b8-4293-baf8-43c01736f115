import { useAppTheme } from '@/app/ThemeContext';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { getResponsiveFontSize, getResponsiveSpacing } from '@/utils/responsive';
import { LinearGradient } from 'expo-linear-gradient';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
    Dimensions,
    SafeAreaView,
    StatusBar,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import LoginModal from './LoginModal';
import RegisterModal from './RegisterModal';

const { width, height } = Dimensions.get('window');

export function WelcomeScreen() {
  const { t } = useTranslation();
  const { colorScheme } = useAppTheme();
  const colors = Colors[colorScheme ?? 'light'];
  
  const [loginModalVisible, setLoginModalVisible] = useState(false);
  const [registerModalVisible, setRegisterModalVisible] = useState(false);

  const handleSwitchToRegister = () => {
    setLoginModalVisible(false);
    setTimeout(() => setRegisterModalVisible(true), 300);
  };

  const handleSwitchToLogin = () => {
    setRegisterModalVisible(false);
    setTimeout(() => setLoginModalVisible(true), 300);
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar 
        barStyle={colorScheme === 'dark' ? 'light-content' : 'dark-content'} 
        backgroundColor={colors.background}
      />
      
      {/* Background Gradient */}
      <LinearGradient
        colors={[
          colors.background,
          colors.tint + '20',
          colors.background,
        ]}
        style={styles.backgroundGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />

      <View style={styles.content}>
        {/* Logo Section */}
        <View style={styles.logoSection}>
          <IconSymbol
            size={120}
            name="lock.shield"
            color={colors.tint}
          />

          <Text style={[styles.appName, { color: colors.text }]}>
            FeeFence
          </Text>

          <Text style={[styles.tagline, { color: colors.textSecondary }]}>
            Smart app blocking with penalty fees
          </Text>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionSection}>
          <TouchableOpacity
            style={[styles.primaryButton, { backgroundColor: colors.tint }]}
            onPress={() => setRegisterModalVisible(true)}
          >
            <Text style={styles.primaryButtonText}>
              {t('auth.getStarted')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.secondaryButton, { borderColor: colors.border }]}
            onPress={() => setLoginModalVisible(true)}
          >
            <Text style={[styles.secondaryButtonText, { color: colors.text }]}>
              {t('auth.signIn')}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Modals */}
      <LoginModal
        visible={loginModalVisible}
        onClose={() => setLoginModalVisible(false)}
        onSwitchToRegister={handleSwitchToRegister}
      />

      <RegisterModal
        visible={registerModalVisible}
        onClose={() => setRegisterModalVisible(false)}
        onSwitchToLogin={handleSwitchToLogin}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: getResponsiveSpacing('lg'),
  },
  logoSection: {
    alignItems: 'center',
    marginBottom: getResponsiveSpacing('xxl'),
  },
  appName: {
    fontSize: getResponsiveFontSize('xxl'),
    fontWeight: '800',
    marginTop: getResponsiveSpacing('lg'),
    marginBottom: getResponsiveSpacing('sm'),
    letterSpacing: 1,
  },
  tagline: {
    fontSize: getResponsiveFontSize('md'),
    textAlign: 'center',
    lineHeight: 24,
    maxWidth: width * 0.8,
  },
  actionSection: {
    width: '100%',
    maxWidth: 300,
    gap: getResponsiveSpacing('md'),
  },
  primaryButton: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: getResponsiveSpacing('lg'),
    borderRadius: 16,
  },
  primaryButtonText: {
    color: '#ffffff',
    fontSize: getResponsiveFontSize('lg'),
    fontWeight: '600',
  },
  secondaryButton: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: getResponsiveSpacing('lg'),
    borderRadius: 16,
    borderWidth: 2,
  },
  secondaryButtonText: {
    fontSize: getResponsiveFontSize('lg'),
    fontWeight: '600',
  },
});

export default WelcomeScreen;
