import React, { createContext, ReactNode, useContext, useState } from 'react';
import { ColorSchemeName, useColorScheme as useDeviceColorScheme } from 'react-native';

// Available theme options
export type ThemeName = 'system' | 'superBlack' | 'boxyBeige';

// Type definition for our theme context
type ThemeContextType = {
  themeName: ThemeName;
  colorScheme: ColorSchemeName;
  setTheme: (theme: ThemeName) => void;
  glowEnabled: boolean;
  setGlowEnabled: (enabled: boolean) => void;
};

// Create the context with default values
export const ThemeContext = createContext<ThemeContextType>({
  themeName: 'system',
  colorScheme: 'light',
  setTheme: () => {},
  glowEnabled: true,
  setGlowEnabled: () => {},
});

// Props for our ThemeProvider component
type ThemeProviderProps = {
  children: ReactNode;
};

// Theme provider component to wrap the app
export function ThemeProvider({ children }: ThemeProviderProps) {
  // Get the device color scheme
  const deviceColorScheme = useDeviceColorScheme();
  
  // State to track the selected theme
  const [themeName, setThemeName] = useState<ThemeName>('system');
  
  // State to track if glow effects are enabled
  const [glowEnabled, setGlowEnabled] = useState<boolean>(true);
  
  // Function to set the theme
  const setTheme = (newTheme: ThemeName) => {
    setThemeName(newTheme);
  };
  
  // Determine the actual color scheme based on theme selection
  const colorScheme: ColorSchemeName = 
    themeName === 'system' ? deviceColorScheme : 
    themeName === 'superBlack' ? 'dark' : 'light';
    
  // Create the context value
  const contextValue = {
    themeName,
    colorScheme,
    setTheme,
    glowEnabled,
    setGlowEnabled,
  };
  
  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
}

// Custom hook to use the theme
export function useAppTheme() {
  return useContext(ThemeContext);
}

// Display names for themes
export const themeLabels = {
  system: 'System Default',
  superBlack: 'Super Black',
  boxyBeige: 'Boxy Beige',
};

// Theme descriptions
export const themeInfo = {
  system: 'Follow system light/dark settings',
  superBlack: 'Dark mode with true black background and silver accents',
  boxyBeige: 'Light mode with warm beige colors and brown accents',
};

export default ThemeProvider;